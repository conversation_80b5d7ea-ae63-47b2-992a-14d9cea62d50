# 跨交易所套保策略配置文件
# Cross-Exchange Hedging Strategy Configuration

# 基础配置
BASIC:
  # 支持的交易所列表
  SUPPORTED_EXCHANGES:
    - binance
    - okx
  
  # 策略运行参数
  STRATEGY:
    MAX_POSITIONS: 10          # 最大持仓数量
    MIN_AMOUNT: 100           # 最小交易金额(USDT)
    MAX_AMOUNT: 10000         # 最大交易金额(USDT)
    CHECK_INTERVAL: 5         # 检查间隔(秒)
    AUTO_START_MONITORING: true  # 是否自动启动监控
    
  # 市场数据配置
  MARKET_DATA:
    UPDATE_INTERVAL: 2        # 市场数据更新间隔(秒)
    MAX_AGE: 30              # 市场数据最大有效期(秒)
    CACHE_SIZE: 1000         # 缓存大小

# 风险管理配置
RISK_MANAGEMENT:
  # 风险限制
  LIMITS:
    MAX_TOTAL_EXPOSURE: 100000    # 最大总敞口(USDT)
    MAX_SINGLE_POSITION: 10000    # 单笔最大持仓(USDT)
    MAX_DAILY_TRADES: 50          # 每日最大交易次数
    MAX_CONCENTRATION: 0.3        # 最大集中度(30%)
    MAX_CORRELATION: 0.8          # 最大相关性(80%)
    STOP_LOSS_THRESHOLD: 0.05     # 止损阈值(5%)
    MAX_DRAWDOWN: 0.1             # 最大回撤(10%)
  
  # 风险监控
  MONITORING:
    CHECK_INTERVAL: 30            # 风险检查间隔(秒)
    ALERT_LEVELS:
      LOW: 40                     # 低风险阈值
      MEDIUM: 60                  # 中风险阈值
      HIGH: 80                    # 高风险阈值
      CRITICAL: 90                # 严重风险阈值

# 持仓管理配置
POSITION_MANAGEMENT:
  # 盈亏计算
  PNL:
    UPDATE_INTERVAL: 10           # 盈亏更新间隔(秒)
    PRECISION: 6                  # 计算精度(小数位)
  
  # 自动平仓
  AUTO_CLOSE:
    ENABLED: true                 # 是否启用自动平仓
    THRESHOLD: 0.05               # 自动平仓阈值(5%)
    MAX_POSITION_AGE: 86400       # 最大持仓时间(秒，24小时)
  
  # 止损止盈
  STOP_LOSS:
    ENABLED: true                 # 是否启用止损
    THRESHOLD: -0.1               # 止损阈值(-10%)
  
  TAKE_PROFIT:
    ENABLED: true                 # 是否启用止盈
    THRESHOLD: 0.2                # 止盈阈值(20%)

# 交易所协调配置
COORDINATOR:
  # 网络配置
  NETWORK:
    TIMEOUT: 30                   # 网络超时(秒)
    MAX_RETRIES: 3                # 最大重试次数
    RETRY_DELAY: 1                # 重试延迟(秒)
  
  # 延迟监控
  LATENCY:
    MONITOR_INTERVAL: 10          # 延迟监控间隔(秒)
    MAX_LATENCY: 5000             # 最大延迟(毫秒)
    ALERT_LATENCY: 1000           # 延迟警告阈值(毫秒)
  
  # 订单执行
  ORDER_EXECUTION:
    SIMULTANEOUS_TIMEOUT: 30      # 同时执行订单超时(秒)
    PARTIAL_FILL_HANDLING: true   # 是否处理部分成交

# 预定义套保条件模板
CONDITION_TEMPLATES:
  # 价差套保模板
  PRICE_DIFFERENCE:
    DEFAULT:
      TRIGGER_VALUE: 50.0         # 默认触发价差(USDT)
      COMPARISON_OPERATOR: ">"    # 比较运算符
      AMOUNT_USDT: 1000.0         # 默认交易金额
      PRIORITY: 1                 # 优先级
    
    BTC_USDT:
      TRIGGER_VALUE: 100.0        # BTC专用触发价差
      AMOUNT_USDT: 2000.0         # BTC专用交易金额
    
    ETH_USDT:
      TRIGGER_VALUE: 5.0          # ETH专用触发价差
      AMOUNT_USDT: 1500.0         # ETH专用交易金额
  
  # 资金费率差异套保模板
  FUNDING_RATE_DIFF:
    DEFAULT:
      TRIGGER_VALUE: 0.001        # 默认触发资金费率差异(0.1%)
      COMPARISON_OPERATOR: ">"
      AMOUNT_USDT: 1000.0
      PRIORITY: 2
    
    HIGH_VOLUME:
      TRIGGER_VALUE: 0.0005       # 高交易量币种的触发值
      AMOUNT_USDT: 5000.0
  
  # 基差率套保模板
  BASIS_RATE:
    DEFAULT:
      TRIGGER_VALUE: 0.005        # 默认触发基差率(0.5%)
      COMPARISON_OPERATOR: ">"
      AMOUNT_USDT: 1000.0
      PRIORITY: 3
    
    CONSERVATIVE:
      TRIGGER_VALUE: 0.01         # 保守策略触发值(1%)
      AMOUNT_USDT: 500.0

# 常用交易对配置
TRADING_PAIRS:
  MAJOR_PAIRS:
    - symbol: "BTC/USDT"
      min_amount: 500
      max_amount: 20000
      precision: 6
    - symbol: "ETH/USDT"
      min_amount: 300
      max_amount: 15000
      precision: 6
    - symbol: "BNB/USDT"
      min_amount: 100
      max_amount: 5000
      precision: 4
  
  ALTCOIN_PAIRS:
    - symbol: "ADA/USDT"
      min_amount: 100
      max_amount: 2000
      precision: 4
    - symbol: "DOT/USDT"
      min_amount: 100
      max_amount: 3000
      precision: 4

# 通知配置
NOTIFICATIONS:
  # 钉钉通知
  DINGTALK:
    ENABLED: true                 # 是否启用钉钉通知
    PREFIX: "套保策略"            # 消息前缀
    
    # 通知级别
    LEVELS:
      INFO: true                  # 信息通知
      WARNING: true               # 警告通知
      ERROR: true                 # 错误通知
      CRITICAL: true              # 严重通知
    
    # 通知频率限制
    RATE_LIMIT:
      MAX_MESSAGES_PER_MINUTE: 10 # 每分钟最大消息数
      COOLDOWN_SECONDS: 60        # 冷却时间(秒)

# 日志配置
LOGGING:
  LEVEL: "INFO"                   # 日志级别
  FORMAT: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  
  # 文件日志
  FILE:
    ENABLED: true                 # 是否启用文件日志
    PATH: "logs/hedging.log"      # 日志文件路径
    MAX_SIZE: "10MB"              # 最大文件大小
    BACKUP_COUNT: 5               # 备份文件数量
  
  # 组件日志级别
  COMPONENTS:
    COORDINATOR: "INFO"
    STRATEGY: "INFO"
    RISK_MANAGER: "INFO"
    POSITION_MANAGER: "INFO"

# 开发和调试配置
DEBUG:
  ENABLED: false                  # 是否启用调试模式
  MOCK_TRADING: false             # 是否使用模拟交易
  SAVE_MARKET_DATA: false         # 是否保存市场数据
  PERFORMANCE_MONITORING: true    # 是否启用性能监控
