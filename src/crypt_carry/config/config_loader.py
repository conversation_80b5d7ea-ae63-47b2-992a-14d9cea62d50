import logging
import os
from pathlib import Path
from typing import Dict, Any

import yaml
from ruamel.yaml import YAML

logger = logging.getLogger(__name__)

class ConfigLoader:
    _config_instance = None
    _trading_config_instance = None
    _ding_talk_config_instance = None
    _hedging_config_instance = None  # 新增：套保配置实例
    _yaml = YAML()  # 创建 YAML 实例
    config_dir = Path(__file__).parent

    @classmethod
    def get_base_config(cls) -> Dict[str, Any]:
        """获取主配置"""
        if cls._config_instance is None:
            config_path = cls.config_dir / 'base_config.yaml'
            with open(config_path, 'r') as f:
                cls._config_instance = cls._yaml.load(f)
        return cls._config_instance

    @classmethod
    def get_trading_config(cls) -> Dict[str, Any]:
        """获取交易配置"""
        if cls._trading_config_instance is None:
            config_path = cls.config_dir / 'trading_config.yaml'
            with open(config_path, 'r') as f:
                cls._trading_config_instance = cls._yaml.load(f)
        return cls._trading_config_instance

    @classmethod
    def get_hedging_config(cls) -> Dict[str, Any]:
        """获取套保策略配置"""
        if cls._hedging_config_instance is None:
            config_path = cls.config_dir / 'hedging_config.yaml'
            try:
                with open(config_path, 'r', encoding='utf-8') as f:
                    cls._hedging_config_instance = cls._yaml.load(f)
                logger.info("成功加载套保策略配置")
            except FileNotFoundError:
                logger.error(f"套保配置文件不存在: {config_path}")
                # 返回默认配置
                cls._hedging_config_instance = cls._get_default_hedging_config()
            except Exception as e:
                logger.error(f"加载套保配置失败: {str(e)}")
                cls._hedging_config_instance = cls._get_default_hedging_config()
        return cls._hedging_config_instance

    @classmethod
    def reload_config(cls, force: bool = True) -> None:
        """重新加载配置

        Args:
            force: 是否强制重新加载，即使配置实例已存在
        """
        if force:
            # 强制重新加载配置文件
            config_path = cls.config_dir / 'base_config.yaml'
            trading_config_path = cls.config_dir / 'trading_config.yaml'
            ding_talk_config_path = os.path.join(cls.config_dir, 'ding_talk_config.yaml')
            hedging_config_path = cls.config_dir / 'hedging_config.yaml'

            with open(config_path, 'r') as f:
                cls._config_instance = cls._yaml.load(f)
            with open(trading_config_path, 'r') as f:
                cls._trading_config_instance = cls._yaml.load(f)

            # 加载钉钉配置
            if os.path.exists(ding_talk_config_path):
                with open(ding_talk_config_path, 'r', encoding='utf-8') as f:
                    cls._ding_talk_config_instance = yaml.safe_load(f).get('DINGTALK', {})
            else:
                logger.warning("钉钉配置文件不存在，使用空配置")
                cls._ding_talk_config_instance = {}

            # 加载套保配置
            if os.path.exists(hedging_config_path):
                with open(hedging_config_path, 'r', encoding='utf-8') as f:
                    cls._hedging_config_instance = cls._yaml.load(f)
            else:
                logger.warning("套保配置文件不存在，使用默认配置")
                cls._hedging_config_instance = cls._get_default_hedging_config()
        else:
            # 仅清除缓存
            cls._config_instance = None
            cls._trading_config_instance = None
            cls._ding_talk_config_instance = None
            cls._hedging_config_instance = None
            cls.get_base_config()
            cls.get_trading_config()
            cls.get_ding_talk_config()
            cls.get_hedging_config()

    @classmethod
    def update_trading_config(cls, updates: Dict[str, Any]) -> None:
        """更新交易配置

        Args:
            updates: 要更新的配置字典，支持多层级更新
                例如: {'STRATEGY': {'FORCE_REBALANCE': False}}
        """

        def update_dict(target: Dict, source: Dict) -> None:
            """递归更新字典"""
            for key, value in source.items():
                if isinstance(value, dict) and key in target and isinstance(target[key], dict):
                    # 如果当前值是字典且目标中存在相同的键也是字典，则递归更新
                    update_dict(target[key], value)
                else:
                    # 否则直接更新值
                    target[key] = value

        # 获取当前配置
        config = cls.get_trading_config()

        # 更新配置
        update_dict(config, updates)

        # 保存到文件
        config_path = cls.config_dir / 'trading_config.yaml'
        with open(config_path, 'w') as f:
            cls._yaml.dump(config, f)

    @classmethod
    def get_ding_talk_config(cls) -> dict:
        """获取钉钉配置

        Returns:
            dict: 钉钉配置
        """
        if cls._ding_talk_config_instance is None:
            config_path = os.path.join(cls.config_dir, 'ding_talk_config.yaml')
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    cls._ding_talk_config_instance = yaml.safe_load(f).get('DINGTALK', {})
            else:
                logger.warning("钉钉配置文件不存在，使用空配置")
                cls._ding_talk_config_instance = {}
        return cls._ding_talk_config_instance

    @classmethod
    def update_ding_talk_config(cls, config: dict):
        """更新钉钉配置

        Args:
            config: 新的钉钉配置
        """
        config_path = os.path.join(cls.config_dir, 'ding_talk_config.yaml')
        with open(config_path, 'w', encoding='utf-8') as f:
            yaml.safe_dump({'DINGTALK': config}, f, allow_unicode=True)
        cls._ding_talk_config_instance = config

    @classmethod
    def update_hedging_config(cls, updates: Dict[str, Any]) -> None:
        """更新套保策略配置

        Args:
            updates: 要更新的配置字典，支持多层级更新
                例如: {'BASIC': {'STRATEGY': {'MAX_POSITIONS': 20}}}
        """
        def update_dict(target: Dict, source: Dict) -> None:
            """递归更新字典"""
            for key, value in source.items():
                if isinstance(value, dict) and key in target and isinstance(target[key], dict):
                    # 如果当前值是字典且目标中存在相同的键也是字典，则递归更新
                    update_dict(target[key], value)
                else:
                    # 否则直接更新值
                    target[key] = value

        # 获取当前配置
        config = cls.get_hedging_config()

        # 更新配置
        update_dict(config, updates)

        # 保存到文件
        config_path = cls.config_dir / 'hedging_config.yaml'
        with open(config_path, 'w', encoding='utf-8') as f:
            cls._yaml.dump(config, f)

        logger.info(f"套保配置已更新: {updates}")

    @classmethod
    def _get_default_hedging_config(cls) -> Dict[str, Any]:
        """获取默认套保配置"""
        return {
            'BASIC': {
                'SUPPORTED_EXCHANGES': ['binance', 'okx'],
                'STRATEGY': {
                    'MAX_POSITIONS': 10,
                    'MIN_AMOUNT': 100,
                    'MAX_AMOUNT': 10000,
                    'CHECK_INTERVAL': 5,
                    'AUTO_START_MONITORING': True
                },
                'MARKET_DATA': {
                    'UPDATE_INTERVAL': 2,
                    'MAX_AGE': 30,
                    'CACHE_SIZE': 1000
                }
            },
            'RISK_MANAGEMENT': {
                'LIMITS': {
                    'MAX_TOTAL_EXPOSURE': 100000,
                    'MAX_SINGLE_POSITION': 10000,
                    'MAX_DAILY_TRADES': 50,
                    'MAX_CONCENTRATION': 0.3,
                    'MAX_CORRELATION': 0.8,
                    'STOP_LOSS_THRESHOLD': 0.05,
                    'MAX_DRAWDOWN': 0.1
                },
                'MONITORING': {
                    'CHECK_INTERVAL': 30,
                    'ALERT_LEVELS': {
                        'LOW': 40,
                        'MEDIUM': 60,
                        'HIGH': 80,
                        'CRITICAL': 90
                    }
                }
            },
            'POSITION_MANAGEMENT': {
                'PNL': {
                    'UPDATE_INTERVAL': 10,
                    'PRECISION': 6
                },
                'AUTO_CLOSE': {
                    'ENABLED': True,
                    'THRESHOLD': 0.05,
                    'MAX_POSITION_AGE': 86400
                },
                'STOP_LOSS': {
                    'ENABLED': True,
                    'THRESHOLD': -0.1
                },
                'TAKE_PROFIT': {
                    'ENABLED': True,
                    'THRESHOLD': 0.2
                }
            },
            'COORDINATOR': {
                'NETWORK': {
                    'TIMEOUT': 30,
                    'MAX_RETRIES': 3,
                    'RETRY_DELAY': 1
                },
                'LATENCY': {
                    'MONITOR_INTERVAL': 10,
                    'MAX_LATENCY': 5000,
                    'ALERT_LATENCY': 1000
                },
                'ORDER_EXECUTION': {
                    'SIMULTANEOUS_TIMEOUT': 30,
                    'PARTIAL_FILL_HANDLING': True
                }
            },
            'NOTIFICATIONS': {
                'DINGTALK': {
                    'ENABLED': True,
                    'PREFIX': '套保策略',
                    'LEVELS': {
                        'INFO': True,
                        'WARNING': True,
                        'ERROR': True,
                        'CRITICAL': True
                    }
                }
            },
            'DEBUG': {
                'ENABLED': False,
                'MOCK_TRADING': False,
                'SAVE_MARKET_DATA': False,
                'PERFORMANCE_MONITORING': True
            }
        }
