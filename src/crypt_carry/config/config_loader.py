import logging
import os
from pathlib import Path
from typing import Dict, Any

import yaml
from ruamel.yaml import YAML

logger = logging.getLogger(__name__)

class ConfigLoader:
    _config_instance = None
    _trading_config_instance = None
    _ding_talk_config_instance = None
    _yaml = YAML()  # 创建 YAML 实例
    config_dir = Path(__file__).parent

    @classmethod
    def get_base_config(cls) -> Dict[str, Any]:
        """获取主配置"""
        if cls._config_instance is None:
            config_path = cls.config_dir / 'base_config.yaml'
            with open(config_path, 'r') as f:
                cls._config_instance = cls._yaml.load(f)
        return cls._config_instance

    @classmethod
    def get_trading_config(cls) -> Dict[str, Any]:
        """获取交易配置"""
        if cls._trading_config_instance is None:
            config_path = cls.config_dir / 'trading_config.yaml'
            with open(config_path, 'r') as f:
                cls._trading_config_instance = cls._yaml.load(f)
        return cls._trading_config_instance

    @classmethod
    def reload_config(cls, force: bool = True) -> None:
        """重新加载配置
        
        Args:
            force: 是否强制重新加载，即使配置实例已存在
        """
        if force:
            # 强制重新加载配置文件
            config_path = cls.config_dir / 'base_config.yaml'
            trading_config_path = cls.config_dir / 'trading_config.yaml'
            ding_talk_config_path = os.path.join(cls.config_dir, 'ding_talk_config.yaml')

            with open(config_path, 'r') as f:
                cls._config_instance = cls._yaml.load(f)
            with open(trading_config_path, 'r') as f:
                cls._trading_config_instance = cls._yaml.load(f)
            if os.path.exists(ding_talk_config_path):
                with open(ding_talk_config_path, 'r', encoding='utf-8') as f:
                    cls._ding_talk_config_instance = yaml.safe_load(f).get('DINGTALK', {})
            else:
                logger.warning("钉钉配置文件不存在，使用空配置")
                cls._ding_talk_config_instance = {}
        else:
            # 仅清除缓存
            cls._config_instance = None
            cls._trading_config_instance = None
            cls._ding_talk_config_instance = None
            cls.get_base_config()
            cls.get_trading_config()
            cls.get_ding_talk_config()

    @classmethod
    def update_trading_config(cls, updates: Dict[str, Any]) -> None:
        """更新交易配置
        
        Args:
            updates: 要更新的配置字典，支持多层级更新
                例如: {'STRATEGY': {'FORCE_REBALANCE': False}}
        """

        def update_dict(target: Dict, source: Dict) -> None:
            """递归更新字典"""
            for key, value in source.items():
                if isinstance(value, dict) and key in target and isinstance(target[key], dict):
                    # 如果当前值是字典且目标中存在相同的键也是字典，则递归更新
                    update_dict(target[key], value)
                else:
                    # 否则直接更新值
                    target[key] = value

        # 获取当前配置
        config = cls.get_trading_config()

        # 更新配置
        update_dict(config, updates)

        # 保存到文件
        config_path = cls.config_dir / 'trading_config.yaml'
        with open(config_path, 'w') as f:
            cls._yaml.dump(config, f)

    @classmethod
    def get_ding_talk_config(cls) -> dict:
        """获取钉钉配置
        
        Returns:
            dict: 钉钉配置
        """
        if cls._ding_talk_config_instance is None:
            config_path = os.path.join(cls.config_dir, 'ding_talk_config.yaml')
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    cls._ding_talk_config_instance = yaml.safe_load(f).get('DINGTALK', {})
            else:
                logger.warning("钉钉配置文件不存在，使用空配置")
                cls._ding_talk_config_instance = {}
        return cls._ding_talk_config_instance

    @classmethod
    def update_ding_talk_config(cls, config: dict):
        """更新钉钉配置
        
        Args:
            config: 新的钉钉配置
        """
        config_path = os.path.join(cls.config_dir, 'ding_talk_config.yaml')
        with open(config_path, 'w', encoding='utf-8') as f:
            yaml.safe_dump({'DINGTALK': config}, f, allow_unicode=True)
        cls._ding_talk_config_instance = config
