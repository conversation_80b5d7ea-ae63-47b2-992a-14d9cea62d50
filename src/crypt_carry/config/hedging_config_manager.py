"""
套保策略配置管理器 - 专门管理套保策略的配置
"""
import logging
from typing import Dict, Any, List, Optional
from dataclasses import dataclass

from crypt_carry.config.config_loader import ConfigLoader

logger = logging.getLogger(__name__)


@dataclass
class HedgingConfigSection:
    """套保配置段"""
    name: str
    data: Dict[str, Any]
    
    def get(self, key: str, default: Any = None) -> Any:
        """获取配置值"""
        return self.data.get(key, default)
    
    def set(self, key: str, value: Any) -> None:
        """设置配置值"""
        self.data[key] = value


class HedgingConfigManager:
    """套保策略配置管理器"""
    
    def __init__(self):
        """初始化配置管理器"""
        self._config_loader = ConfigLoader
        self._config_cache: Optional[Dict[str, Any]] = None
        
    def reload_config(self) -> None:
        """重新加载配置"""
        self._config_cache = None
        self._config_loader.reload_config(force=True)
        logger.info("套保配置已重新加载")
    
    @property
    def config(self) -> Dict[str, Any]:
        """获取完整配置"""
        if self._config_cache is None:
            self._config_cache = self._config_loader.get_hedging_config()
        return self._config_cache
    
    # 基础配置
    @property
    def basic(self) -> HedgingConfigSection:
        """基础配置"""
        return HedgingConfigSection("BASIC", self.config.get("BASIC", {}))
    
    @property
    def supported_exchanges(self) -> List[str]:
        """支持的交易所列表"""
        return self.basic.get("SUPPORTED_EXCHANGES", ["binance", "okx"])
    
    @property
    def strategy_config(self) -> Dict[str, Any]:
        """策略配置"""
        return self.basic.get("STRATEGY", {})
    
    @property
    def max_positions(self) -> int:
        """最大持仓数量"""
        return self.strategy_config.get("MAX_POSITIONS", 10)
    
    @property
    def min_amount(self) -> float:
        """最小交易金额"""
        return self.strategy_config.get("MIN_AMOUNT", 100.0)
    
    @property
    def max_amount(self) -> float:
        """最大交易金额"""
        return self.strategy_config.get("MAX_AMOUNT", 10000.0)
    
    @property
    def check_interval(self) -> int:
        """检查间隔(秒)"""
        return self.strategy_config.get("CHECK_INTERVAL", 5)
    
    @property
    def auto_start_monitoring(self) -> bool:
        """是否自动启动监控"""
        return self.strategy_config.get("AUTO_START_MONITORING", True)
    
    # 风险管理配置
    @property
    def risk_management(self) -> HedgingConfigSection:
        """风险管理配置"""
        return HedgingConfigSection("RISK_MANAGEMENT", self.config.get("RISK_MANAGEMENT", {}))
    
    @property
    def risk_limits(self) -> Dict[str, Any]:
        """风险限制"""
        return self.risk_management.get("LIMITS", {})
    
    @property
    def max_total_exposure(self) -> float:
        """最大总敞口"""
        return self.risk_limits.get("MAX_TOTAL_EXPOSURE", 100000.0)
    
    @property
    def max_single_position(self) -> float:
        """单笔最大持仓"""
        return self.risk_limits.get("MAX_SINGLE_POSITION", 10000.0)
    
    @property
    def max_daily_trades(self) -> int:
        """每日最大交易次数"""
        return self.risk_limits.get("MAX_DAILY_TRADES", 50)
    
    @property
    def stop_loss_threshold(self) -> float:
        """止损阈值"""
        return self.risk_limits.get("STOP_LOSS_THRESHOLD", 0.05)
    
    # 持仓管理配置
    @property
    def position_management(self) -> HedgingConfigSection:
        """持仓管理配置"""
        return HedgingConfigSection("POSITION_MANAGEMENT", self.config.get("POSITION_MANAGEMENT", {}))
    
    @property
    def pnl_update_interval(self) -> int:
        """盈亏更新间隔"""
        return self.position_management.get("PNL", {}).get("UPDATE_INTERVAL", 10)
    
    @property
    def auto_close_enabled(self) -> bool:
        """是否启用自动平仓"""
        return self.position_management.get("AUTO_CLOSE", {}).get("ENABLED", True)
    
    @property
    def auto_close_threshold(self) -> float:
        """自动平仓阈值"""
        return self.position_management.get("AUTO_CLOSE", {}).get("THRESHOLD", 0.05)
    
    @property
    def stop_loss_enabled(self) -> bool:
        """是否启用止损"""
        return self.position_management.get("STOP_LOSS", {}).get("ENABLED", True)
    
    @property
    def take_profit_enabled(self) -> bool:
        """是否启用止盈"""
        return self.position_management.get("TAKE_PROFIT", {}).get("ENABLED", True)
    
    @property
    def take_profit_threshold(self) -> float:
        """止盈阈值"""
        return self.position_management.get("TAKE_PROFIT", {}).get("THRESHOLD", 0.2)
    
    # 协调器配置
    @property
    def coordinator(self) -> HedgingConfigSection:
        """协调器配置"""
        return HedgingConfigSection("COORDINATOR", self.config.get("COORDINATOR", {}))
    
    @property
    def network_timeout(self) -> int:
        """网络超时"""
        return self.coordinator.get("NETWORK", {}).get("TIMEOUT", 30)
    
    @property
    def max_retries(self) -> int:
        """最大重试次数"""
        return self.coordinator.get("NETWORK", {}).get("MAX_RETRIES", 3)
    
    @property
    def simultaneous_timeout(self) -> int:
        """同时执行订单超时"""
        return self.coordinator.get("ORDER_EXECUTION", {}).get("SIMULTANEOUS_TIMEOUT", 30)
    
    # 条件模板配置
    @property
    def condition_templates(self) -> Dict[str, Any]:
        """条件模板配置"""
        return self.config.get("CONDITION_TEMPLATES", {})
    
    def get_condition_template(self, condition_type: str, template_name: str = "DEFAULT") -> Dict[str, Any]:
        """获取条件模板
        
        Args:
            condition_type: 条件类型 (PRICE_DIFFERENCE, FUNDING_RATE_DIFF, BASIS_RATE)
            template_name: 模板名称
            
        Returns:
            Dict[str, Any]: 模板配置
        """
        templates = self.condition_templates.get(condition_type, {})
        return templates.get(template_name, templates.get("DEFAULT", {}))
    
    # 交易对配置
    @property
    def trading_pairs(self) -> Dict[str, Any]:
        """交易对配置"""
        return self.config.get("TRADING_PAIRS", {})
    
    def get_pair_config(self, symbol: str) -> Optional[Dict[str, Any]]:
        """获取交易对配置
        
        Args:
            symbol: 交易对符号
            
        Returns:
            Optional[Dict[str, Any]]: 交易对配置，不存在返回None
        """
        # 先在主要交易对中查找
        major_pairs = self.trading_pairs.get("MAJOR_PAIRS", [])
        for pair in major_pairs:
            if pair.get("symbol") == symbol:
                return pair
        
        # 再在山寨币交易对中查找
        altcoin_pairs = self.trading_pairs.get("ALTCOIN_PAIRS", [])
        for pair in altcoin_pairs:
            if pair.get("symbol") == symbol:
                return pair
        
        return None
    
    # 通知配置
    @property
    def notifications(self) -> HedgingConfigSection:
        """通知配置"""
        return HedgingConfigSection("NOTIFICATIONS", self.config.get("NOTIFICATIONS", {}))
    
    @property
    def dingtalk_enabled(self) -> bool:
        """是否启用钉钉通知"""
        return self.notifications.get("DINGTALK", {}).get("ENABLED", True)
    
    @property
    def dingtalk_prefix(self) -> str:
        """钉钉消息前缀"""
        return self.notifications.get("DINGTALK", {}).get("PREFIX", "套保策略")
    
    # 调试配置
    @property
    def debug(self) -> HedgingConfigSection:
        """调试配置"""
        return HedgingConfigSection("DEBUG", self.config.get("DEBUG", {}))
    
    @property
    def debug_enabled(self) -> bool:
        """是否启用调试模式"""
        return self.debug.get("ENABLED", False)
    
    @property
    def mock_trading(self) -> bool:
        """是否使用模拟交易"""
        return self.debug.get("MOCK_TRADING", False)
    
    # 配置更新方法
    def update_strategy_config(self, **kwargs) -> None:
        """更新策略配置
        
        Args:
            **kwargs: 策略配置参数
        """
        updates = {"BASIC": {"STRATEGY": kwargs}}
        self._config_loader.update_hedging_config(updates)
        self._config_cache = None  # 清除缓存
    
    def update_risk_limits(self, **kwargs) -> None:
        """更新风险限制
        
        Args:
            **kwargs: 风险限制参数
        """
        updates = {"RISK_MANAGEMENT": {"LIMITS": kwargs}}
        self._config_loader.update_hedging_config(updates)
        self._config_cache = None
    
    def update_position_config(self, **kwargs) -> None:
        """更新持仓配置
        
        Args:
            **kwargs: 持仓配置参数
        """
        updates = {"POSITION_MANAGEMENT": kwargs}
        self._config_loader.update_hedging_config(updates)
        self._config_cache = None
    
    def update_config(self, section: str, **kwargs) -> None:
        """更新指定段的配置
        
        Args:
            section: 配置段名称
            **kwargs: 配置参数
        """
        updates = {section: kwargs}
        self._config_loader.update_hedging_config(updates)
        self._config_cache = None
    
    def get_config_summary(self) -> Dict[str, Any]:
        """获取配置摘要"""
        return {
            "supported_exchanges": self.supported_exchanges,
            "max_positions": self.max_positions,
            "amount_range": f"{self.min_amount}-{self.max_amount} USDT",
            "check_interval": f"{self.check_interval}s",
            "risk_limits": {
                "max_total_exposure": self.max_total_exposure,
                "max_single_position": self.max_single_position,
                "stop_loss_threshold": f"{self.stop_loss_threshold:.1%}"
            },
            "auto_features": {
                "monitoring": self.auto_start_monitoring,
                "close": self.auto_close_enabled,
                "stop_loss": self.stop_loss_enabled,
                "take_profit": self.take_profit_enabled
            },
            "debug_mode": self.debug_enabled,
            "mock_trading": self.mock_trading
        }
