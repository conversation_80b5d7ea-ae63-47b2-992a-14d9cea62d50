import json
import time as time_module  # 重命名 time 模块以避免混淆
from datetime import datetime
from typing import Dict

from crypt_carry.config.config_loader import ConfigLoader
from crypt_carry.utils.data_directory import DataDirectory
from crypt_carry.utils.logger import logger


class ArbitrageManager:
    def __init__(self, config: Dict = None,
                 duplicate_interval_seconds=300,  # 5分钟
                 file_rotation_interval_hours=1  # 1小时
                 ):
        self.config = config or ConfigLoader.get_base_config()

        # 使用DataDirectory管理数据目录
        data_dir = DataDirectory(self.config)
        self.arbitrage_dir = data_dir.get_path("ARBITRAGE")
        self.arbitrage_dir.mkdir(parents=True, exist_ok=True)

        # 配置参数
        self.duplicate_interval = duplicate_interval_seconds
        self.file_rotation_interval = file_rotation_interval_hours

        # 当天的套利机会文件
        self.current_arbitrage_file = None
        self.last_arbitrage_date = None
        self.last_arbitrage_hour = None

        # 用于去重的字典
        self.processed_pairs = {}

        # 初始化当天的文件
        self._init_current_file()

    def _init_current_file(self):
        """初始化当天的文件"""
        current_time = datetime.now()
        current_date = current_time.date()
        current_hour = current_time.hour // self.file_rotation_interval * self.file_rotation_interval

        # 检查是否需要创建新的小时文件
        if (self.last_arbitrage_date != current_date or
                self.last_arbitrage_hour != current_hour):

            self.last_arbitrage_date = current_date
            self.last_arbitrage_hour = current_hour

            # 生成新的小时文件名
            self.current_arbitrage_file = self.get_arbitrage_filename()

            # 如果文件不存在，创建一个空的数组
            if not self.current_arbitrage_file.exists():
                self.current_arbitrage_file.write_text('[]', encoding='utf-8')

    def get_arbitrage_filename(self):
        """获取当天的套利机会文件名（按指定间隔）"""
        current_datetime = datetime.now()
        current_hour = current_datetime.hour // self.file_rotation_interval * self.file_rotation_interval
        filename = f"arbitrage_opportunities_{current_datetime.strftime('%Y%m%d')}_{current_hour:02d}.json"
        return self.arbitrage_dir / filename

    def is_duplicate_opportunity(self, pair, timestamp):
        """
        检查是否是重复的套利机会
        按指定间隔去重
        """
        current_time = datetime.fromtimestamp(timestamp / 1000)

        if pair in self.processed_pairs:
            last_processed_time = self.processed_pairs[pair]
            # 如果在指定间隔内已处理过，视为重复
            if (current_time - last_processed_time).total_seconds() < self.duplicate_interval:
                return True

        # 更新处理时间
        self.processed_pairs[pair] = current_time
        return False

    def save_opportunity(self, base_currency: str, quote_currency: str,
                         spot_info: Dict, futures_info: Dict,
                         basis: float, basis_rate: float):
        """保存套利机会"""
        try:
            self._init_current_file()

            # 准备新的套利机会数据
            opportunity = {
                'timestamp': int(time_module.time() * 1000),
                'pair': f"{base_currency}/{quote_currency}",
                'spot': {
                    'exchange': spot_info['exchange'],
                    'price': float(spot_info['price']),
                    'timestamp': int(spot_info['timestamp'])
                },
                'futures': {
                    'exchange': futures_info['exchange'],
                    'price': float(futures_info['price']),
                    'timestamp': int(futures_info['timestamp'])
                },
                'basis': float(basis),
                'basis_rate': float(basis_rate)
            }

            # 检查是否重复
            if self.is_duplicate_opportunity(opportunity['pair'], opportunity['timestamp']):
                return

            # 读取现有数据
            try:
                with open(self.current_arbitrage_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    existing_data = json.loads(content) if content.strip() else []
            except json.JSONDecodeError:
                logger.error(f"JSON解析错误，重置文件内容")
                existing_data = []

            # 添加新数据
            existing_data.append(opportunity)

            # 写入文件
            with open(self.current_arbitrage_file, 'w', encoding='utf-8') as f:
                json.dump(existing_data, f, indent=2, ensure_ascii=False)

            logger.info(f"保存套利机会 - {base_currency}/{quote_currency} "
                        f"基差率: {basis_rate:.4f}% "
                        f"现货: {spot_info['exchange']}({spot_info['price']:.8f}) "
                        f"合约: {futures_info['exchange']}({futures_info['price']:.8f})")

        except Exception as e:
            logger.error(f"保存套利机会失败: {str(e)}")
