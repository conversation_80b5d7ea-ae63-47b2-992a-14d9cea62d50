import logging
from typing import Dict, List, Optional

import ccxt.pro as ccxtpro

from crypt_carry.core.exchange.base.exchange_client import ExchangeClient
from crypt_carry.core.exchange.base.exchange_config import ExchangeConfig
from crypt_carry.core.exchange.funding.funding_rate_scheduler import FundingRateScheduler
from crypt_carry.core.exchange.market.market_limits import MarketLimitManager
from crypt_carry.core.providers.okx.okx_funding import OkxFundingManager
from crypt_carry.core.providers.okx.okx_market import OkxMarketManager
from crypt_carry.core.providers.okx.okx_order_client import OKXOrderClient
from crypt_carry.core.providers.okx.okx_position_rest import OkxPositionRest
from crypt_carry.core.providers.okx.okx_symbol_utils import (
    to_standard_symbol,
    to_internal_symbol
)
from crypt_carry.core.providers.okx.okx_ws import OkxWebSocket
from util.log_throttler import LogThrottler

# 创建logger
logger = logging.getLogger(__name__)


class OkxClient(ExchangeClient):
    """OKX交易所客户端"""

    def __init__(self, config: ExchangeConfig):
        """初始化OKX客户端
        
        Args:
            config: 交易所配置
        """
        # 调用父类初始化
        super().__init__(config)

        # 初始化WebSocket客户端
        self.ws_manager = OkxWebSocket(self.config, self.handle_ws_message)

        # 初始化交易对列表
        self.base_pair = []
        self.spot_pairs = []
        self.perpetual_pairs = []
        self.all_pair = []

        # 初始化CCXT交易所对象
        self.exchange = ccxtpro.okx({
            'apiKey': self.config.api_key,
            'secret': self.config.api_secret,
            'password': self.config.api_config.get("PASSWORD"),  # 添加 password 参数
            'timeout': self.config.timeout,
            'enableRateLimit': True,
            'options': {
                'defaultType': 'swap',
            }
        })

        self.funding_manager: Optional[OkxFundingManager] = None
        self.funding_rate_scheduler: Optional[FundingRateScheduler] = None
        
        if self.config.proxy_enabled:
            # 只设置 httpsProxy，因为 OKX API 使用 HTTPS
            logger.info(
                f"{self.exchange_name} 设置代理: http_proxy={self.config.http_proxy}, ws_proxy={self.config.ws_proxy}")
            self.exchange.httpsProxy = self.config.http_proxy
            self.exchange.wsProxy = self.config.ws_proxy
        
        # 初始化市场限制管理器
        self.market_limit_manager = MarketLimitManager(self.exchange, self.exchange_name)

        # 初始化订单客户端
        self.order_client = OKXOrderClient(self.exchange, self.exchange_name, self.market_limit_manager)

        # 初始化市场数据管理器
        self.market_manager = OkxMarketManager(self.config, self.exchange)

        # 初始化日志频率控制器
        self.log_throttler = LogThrottler.get_instance(f"{self.exchange_name}_okx_client")
        self._15m_log_key = f"{self.exchange_name}_okx_client"
        self.log_throttler.set_interval(self._15m_log_key, 3 * 60)

    async def initialize(self) -> bool:
        """初始化OKX客户端
        
        Returns:
            bool: 是否初始化成功
        """
        try:
            # 加载市场数据
            if not await self.market_manager.load_markets_with_retry():
                logger.error(f"{self.exchange_name} 加载市场失败")
                return False

            # 获取交易对列表
            self.base_pair, self.spot_pairs, self.perpetual_pairs, self.all_pair, self.detect_exclude_pairs = self.market_manager.get_trading_pairs()
            logger.info(f"{self.exchange_name} 获取交易对列表成功, base_pair: {self.base_pair}, spot_pairs: {self.spot_pairs}, perpetual_pairs: {self.perpetual_pairs}, all_pair: {self.all_pair}, detect_exclude_pairs: {self.detect_exclude_pairs}")
            # 初始化市场限制管理器
            await self.market_limit_manager.init_market_limits(self.all_pair)
            
            return True
        except Exception as e:
            logger.error(f"{self.exchange_name} 初始化失败: {str(e)}")
            return False

    async def connect(self):
        """建立WebSocket连接"""
        try:
            await self.ws_manager.connect()
            logger.info(f"{self.exchange_name} WebSocket连接成功")
        except Exception as e:
            logger.error(f"{self.exchange_name} WebSocket连接失败: {str(e)}")
            raise

    async def fetch_spot_balance(self) -> Dict[str, float]:
        """获取现货账户USDT和USDC余额"""
        try:
            balance = await self.exchange.fetch_balance({'type': 'spot'})
            balances = {
                'TOTAL_USDT': float(balance.get('total', {}).get('USDT', 0)),
                'FREE_USDT': float(balance.get('free', {}).get('USDT', 0)),
                'TOTAL_USDC': float(balance.get('total', {}).get('USDC', 0)),
                'FREE_USDC': float(balance.get('free', {}).get('USDC', 0))
            }
            logger.info(f"{self.exchange_name} 现货账户余额: {balances}")
            return balances
        except Exception as e:
            logger.error(f"{self.exchange_name} 获取现货账户余额失败: {str(e)}")
            return {'TOTAL_USDT': 0.0, 'FREE_USDT': 0.0, 'TOTAL_USDC': 0.0, 'FREE_USDC': 0.0}

    async def fetch_futures_balance(self) -> Dict[str, float]:
        """获取合约账户USDT和USDC余额"""
        try:
            balance = await self.exchange.fetch_balance({'type': 'swap'})  # OKX使用'swap'表示合约账户
            balances = {
                'TOTAL_USDT': float(balance.get('total', {}).get('USDT', 0)),
                'FREE_USDT': float(balance.get('free', {}).get('USDT', 0)),
                'TOTAL_USDC': float(balance.get('total', {}).get('USDC', 0)),
                'FREE_USDC': float(balance.get('free', {}).get('USDC', 0))
            }
            logger.info(f"{self.exchange_name} 合约账户余额: {balances}")
            return balances
        except Exception as e:
            logger.error(f"{self.exchange_name} 获取合约账户余额失败: {str(e)}")
            return {'TOTAL_USDT': 0.0, 'FREE_USDT': 0.0, 'TOTAL_USDC': 0.0, 'FREE_USDC': 0.0}

    async def subscribe_market_data(self) -> bool:
        """订阅市场数据
        
        Returns:
            bool: 订阅是否成功
        """
        try:
            # 确保WebSocket已连接
            if not self.ws_manager.is_connected:
                await self.ws_manager.connect()

            # 获取所有交易对
            if not self.spot_pairs and not self.perpetual_pairs:
                logger.warning(f"{self.exchange_name} 没有可订阅的交易对")
                return False

            # 订阅市场数据
            success = await self.ws_manager.subscribe_market_data(self.spot_pairs, self.perpetual_pairs)
            if not success:
                logger.error(f"{self.exchange_name} 订阅市场数据失败")
                return False

            logger.info(
                f"{self.exchange_name} 成功订阅市场数据: {list(self.spot_pairs) + list(self.perpetual_pairs)}")
            return True

        except Exception as e:
            logger.error(f"{self.exchange_name} 订阅市场数据失败: {str(e)}")
            return False

    async def handle_message(self, message: Dict, message_type: str = None):
        """处理WebSocket消息（实现抽象方法）"""
        try:
            await self.handle_ws_message(message)
        except Exception as e:
            logger.error(f"{self.exchange_name} 处理消息失败: {str(e)}")

    async def handle_ws_message(self, message: Dict):
        """处理WebSocket消息"""
        try:
            logger.debug(f"{self.exchange_name} 处理WebSocket消息: {message}")

            # 从 message 获取基本信息
            symbol = message.get('symbol')
            if not symbol:
                logger.warning(f"{self.exchange_name} 消息中没有交易对信息: {message}")
                return

            # 处理价格数据
            price = message.get('last')
            timestamp = message.get('timestamp')

            if price is not None and timestamp is not None:
                # 确定是现货还是合约 (检查 symbol 格式)

                normalized_symbol = to_internal_symbol(symbol)
                is_swap = ':USDT' in normalized_symbol
                standard_symbol = to_standard_symbol(normalized_symbol)
                update_data = {
                    'timestamp': timestamp,
                    f"{'futures' if is_swap else 'spot'}_price": float(price)
                }

                # 更新市场数据
                self.market_manager.update_market_data(standard_symbol, update_data)

                # 记录价格更新
                ws_type = '合约' if is_swap else '现货'
                self.log_throttler.log_if_allowed(
                    logger_obj=logger, key=self._15m_log_key,
                    message=f"{self.exchange_name} 更新{ws_type}价格 - {standard_symbol}={price}"
                )

            else:
                logger.warning(f"{self.exchange_name} 价格或时间戳数据缺失: {message}")

        except Exception as e:
            logger.error(f"{self.exchange_name} 处理WebSocket消息失败: {str(e)}")

    def get_perpetual_pairs(self) -> List[str]:
        """获取永续合约交易对列表
        
        Returns:
            List[str]: 永续合约交易对列表
        """
        return self.perpetual_pairs

    async def start(self):
        """启动OKX客户端"""
        try:
            # 加载市场数据
            if not await self.initialize():
                logger.error(f"{self.exchange_name} 加载市场数据失败")
                return False

            # 初始化资金费率管理器
            logger.info(f"{self.exchange_name} 初始化资金费率管理器, 交易对: {self.perpetual_pairs}")
            self.funding_manager = OkxFundingManager(self.config, self.perpetual_pairs)
            await self.funding_manager.initialize()

            # 启动资金费率定时任务
            self.funding_rate_scheduler = FundingRateScheduler({
                self.exchange_name: self.funding_manager
            }, self.perpetual_pairs)
            await self.funding_rate_scheduler.start()

            # 启动WebSocket连接
            await self.ws_manager.connect()

            # 初始化持仓管理器
            # self.position_manager = OkxPositionWebsocket(
            #     self.exchange,
            #     self.market_limit_manager,
            #     self.all_pair,
            #     self.base_pair,
            #     self.config.trading_quotes
            # )
            # await self.position_manager.start()

            # 初始化持仓管理器
            self.position_rest = OkxPositionRest(
                self.exchange,
                self.exchange_name,
                self.market_limit_manager,
                self.all_pair,
                self.base_pair,
                self.config.trading_quotes
            )

            # 订阅数据
            await self.subscribe_market_data()

            logger.info(f"{self.exchange_name} 客户端启动成功")
            return True

        except Exception as e:
            logger.error(f"{self.exchange_name} 客户端启动失败: {e}")
            raise

    async def stop(self):
        """停止OKX客户端"""
        try:
            # 停止资金费率定时任务
            if hasattr(self, 'funding_rate_scheduler') and self.funding_rate_scheduler is not None:
                await self.funding_rate_scheduler.stop()

            # 关闭WebSocket连接
            if hasattr(self, 'ws_manager') and self.ws_manager is not None:
                await self.ws_manager.close()

            logger.info(f"{self.exchange_name} 客户端已停止")

        except Exception as e:
            logger.error(f"{self.exchange_name} 客户端停止失败: {e}")

    async def close(self):
        """关闭所有连接"""
        try:
            # 停止资金费率调度器
            if hasattr(self, 'funding_rate_scheduler') and self.funding_rate_scheduler is not None:
                await self.funding_rate_scheduler.stop()

            # 关闭WebSocket连接
            if hasattr(self, 'ws_manager') and self.ws_manager is not None:
                await self.ws_manager.close()

            # 关闭交易所连接
            if hasattr(self, 'exchange') and self.exchange is not None:
                await self.exchange.close()
                logger.info(f"{self.exchange_name} CCXT 交易所实例已关闭")

            logger.info(f"{self.exchange_name} 所有连接已关闭")
        except Exception as e:
            logger.error(f"{self.exchange_name} 关闭连接失败: {e}")

    async def create_order(
            self,
            symbol: str,
            order_type: str,
            side: str,
            quantity: float,
            price: Optional[float] = None,
            is_spot: bool = True,
            position_side: Optional[str] = None,
            reduce_only: bool = False,
    ) -> Dict:
        """创建订单"""
        return await self.order_client.create_order(
            symbol=symbol,
            order_type=order_type,
            side=side,
            quantity=quantity,
            price=price,
            is_spot=is_spot,
            position_side=position_side,
            reduce_only=reduce_only,
        )

    async def fetch_order(self, order_id: str, symbol: str, is_spot: bool = True) -> Dict:
        """查询订单信息"""
        return await self.order_client.fetch_order(order_id, symbol, is_spot)

    async def cancel_order(self, order_id: str, symbol: str, is_spot: bool = True) -> Dict:
        """取消订单"""
        return await self.order_client.cancel_order(order_id, symbol, is_spot)

    async def fetch_open_orders(self, symbol: str = None, is_spot: bool = True) -> List[Dict]:
        """查询当前未完成的订单"""
        return await self.order_client.fetch_open_orders(symbol, is_spot)

    async def fetch_closed_orders(self, symbol: str = None, is_spot: bool = True) -> List[Dict]:
        """查询历史订单"""
        return await self.order_client.fetch_closed_orders(symbol, is_spot)

    async def fetch_positions_by_ws(self) -> List[Dict]:
        """通过WebSocket获取持仓数据"""
        return await self.position_manager.get_positions()

    async def fetch_positions_by_rest(self) -> List[Dict]:
        return await self.position_rest.fetch_positions_rest(self.all_pair)

    def get_standard_symbol(self, symbol: str) -> str:
        """将交易所特定的交易对格式转换为标准化格式
        Args:
            symbol: 任意格式的交易对，可能是交易所特定格式或内部格式
            
        Returns:
            str: 标准化格式的交易对，如 BTC/USDT
        """
        try:
            return to_standard_symbol(symbol)
        except Exception as e:
            logger.error(f"转换交易对格式错误: {str(e)}, 原始交易对: {symbol}")
            # 如果转换失败，返回原始交易对
            return symbol

    async def reload_detect_exclude_pairs(self):
        self.detect_exclude_pairs = await self.market_manager.load_detect_exclude_pairs()
