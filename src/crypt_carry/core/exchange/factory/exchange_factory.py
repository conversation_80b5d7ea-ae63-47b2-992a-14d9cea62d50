"""
交易所工厂类 - 负责创建各类交易所客户端
"""
import logging
from typing import Optional

from crypt_carry.core.exchange.base.exchange_client import ExchangeClient
from crypt_carry.core.exchange.base.exchange_config import ExchangeConfig
from crypt_carry.core.providers.binance.binance_client import BinanceClient
from crypt_carry.core.providers.okx.okx_client import OkxClient

logger = logging.getLogger(__name__)


class ExchangeFactory:
    """交易所工厂类，负责创建不同的交易所客户端"""

    @staticmethod
    def create_exchange_client(exchange_name: str, config: ExchangeConfig) -> Optional[ExchangeClient]:
        """创建交易所客户端

        Args:
            exchange_name: 交易所名称
            config: 交易所配置

        Returns:
            ExchangeClient: 交易所客户端，如果不支持则返回None
        """
        exchange_name = exchange_name.upper()

        try:
            if exchange_name == 'BINANCE':
                return BinanceClient(config)
            elif exchange_name == 'OKX':
                return OkxClient(config)
            else:
                logger.warning(f"不支持的交易所类型: {exchange_name}")
                return None
        except Exception as e:
            logger.error(f"创建{exchange_name}交易所客户端失败: {str(e)}")
            return None
