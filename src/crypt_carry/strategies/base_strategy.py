"""
基础策略类
"""
import asyncio
import logging
from abc import ABC, abstractmethod
from typing import Dict

from crypt_carry.core.exchange.account.account_manager import AccountManager
from crypt_carry.core.exchange.base.exchange_client import ExchangeClient
from crypt_carry.core.exchange.base.exchange_config import ExchangeConfig
from crypt_carry.core.exchange.market.market_data_manager import MarketDataManager
from crypt_carry.core.trading.position.position_manager import PositionManager
from crypt_carry.utils.ding_talk_manager import DingTalkManager

logger = logging.getLogger(__name__)

class BaseStrategy(ABC):
    """基础策略类"""

    def __init__(self, exchange_client: ExchangeClient):
        """初始化
        
        Args:
            exchange_client: 交易所客户端
        """
        # 交易所客户端
        self.exchange_client = exchange_client
        self.exchange_name = exchange_client.exchange_name
        
        # 加载配置
        self.config = ExchangeConfig(self.exchange_name)
        self.leverage = 2  # 默认杠杆
        self.max_positions = 5  # 默认最大持仓数量
        # 最大重试次数
        self.max_retries = self.config.get_order_value("MAX_RETRIES", 3)
        # 重试间隔（秒）
        self.retry_interval = self.config.get_order_value("RETRY_INTERVAL_MS", 1000) / 1000
        
        self.strategy_config = {}
        self.scoring_config = {}

        # 初始化管理器
        self.account_manager = AccountManager.get_instance()
        self.position_manager: PositionManager = PositionManager.get_instance()

        # 初始化市场数据管理器
        self.market_data_manager = MarketDataManager.get_instance()

        # 设置账户管理器的交易所客户端
        self.account_manager.set_exchange_client(exchange_client)

        self.is_running = False
        self._stop_event = asyncio.Event()

        # 初始化钉钉消息管理器
        self.ding_talk_manager = DingTalkManager.get_instance()

        # 加载交易所特有配置
        self.load_exchange_config()

    @abstractmethod
    async def run(self):
        """运行策略
        
        此方法需要在子类中实现，用于定义策略的具体运行逻辑。
        """
        pass

    async def _get_total_balance(self) -> float:
        """获取账户总余额"""
        try:
            total_balance = await self.account_manager.get_account_balance(self.exchange_name)
            logger.info(f"账户总余额: {total_balance:.2f} USDT")
            return total_balance
        except Exception as e:
            logger.error(f"获取账户余额失败: {str(e)}")
            return 0.0


    async def _execute_orders(self, position: Dict, funding_rate: float = None, basis_rate: float = None):
        """执行订单
        
        Args:
            position: 交易机会信息，包含：
                {
                    'symbol': symbol,
                    'direction': direction,
                    'trade_type': TradeType.CREATE,
                    'spot_amt': spot_amt,  # 现货可用余额
                    'future_amt': future_amt,   # 合约可用余额
                    'spot_quantity': spot_quantity,  # 现货数量,平仓时使用
                    'future_quantity': future_quantity  # 合约数量，平仓时使用
                }
            funding_rate: 资金费率
            basis_rate: 基差率
        """
        try:
            # 定义订单失败回调
            async def on_order_failed(symbol: str, failed_position: Dict):
                """订单失败回调函数
                
                Args:
                    symbol: 交易对
                    failed_position: 失败的订单信息
                """
                # 获取当前重试次数
                retry_count = failed_position.get('retry_count', 0)

                # 如果超过最大重试次数,则终止重试
                if retry_count >= self.max_retries:
                    error_msg = f"{self.exchange_name} {symbol} 订单执行失败,已达到最大重试次数 {self.max_retries}"
                    logger.error(error_msg)
                    self.ding_talk_manager.send_message(error_msg, prefix=self.exchange_name)
                    return

                try:
                    logger.warning(f"{self.exchange_name} {symbol} 订单执行失败，第 {retry_count + 1} 次重试")

                    # 增加重试计数
                    failed_position['retry_count'] = retry_count + 1

                    # 重新执行订单
                    await self.order_executor.execute_orders(failed_position, on_order_failed)

                except Exception as e:
                    error_msg = f"{self.exchange_name} {symbol} 订单重试失败,放弃本次套利.: {str(e)}"
                    logger.error(error_msg)
                    self.ding_talk_manager.send_message(error_msg, prefix=self.exchange_name)

            # 执行订单
            success = await self.order_executor.execute_orders(position, on_order_failed)

            if success:
                # 发送成功消息
                message = (
                    f"订单执行成功\n"
                    f"交易所: {self.exchange_name}\n"
                    f"交易对: {position['symbol']}\n"
                    f"方向: {position['direction']}\n"
                    f"类型: {position['trade_type']}"
                )
                logger.debug(message)
                self.ding_talk_manager.send_message(message, prefix=self.exchange_name)
                
        except Exception as e:
            logger.error(f"{self.exchange_name} 执行订单失败: {str(e)}")
            self.ding_talk_manager.send_message(f"{self.exchange_name} 执行订单失败: {str(e)}",
                                                prefix=self.exchange_name)
            raise

    def _init_configuration(self):
        """初始化配置"""

        self.market_data_age_sec = self.config.trading_config.get('MARKET_DATA_AGE_SEC', 10)
        # 从config中读取策略配置
        self.strategy_config = self.config.get_strategy_config()

        # 读取打分配置
        self.scoring_config = self.strategy_config.get('SCORING_CONFIG', {})

        # 读取杠杆配置
        self.leverage = self.strategy_config.get('LEVERAGE', 2)

        # 读取最大持仓数量
        self.max_positions = self.strategy_config.get('MAX_POSITIONS', 5)

    async def reload_config(self):
        """重新加载配置"""
        try:
            logger.debug("重新加载配置...")
            # 强制重新加载配置
            self.config.reload_config(force=True)

            # 重新初始化配置变量
            self._init_configuration()
            await self.exchange_client.reload_detect_exclude_pairs()
            logger.debug("配置加载完成")
        except Exception as e:
            logger.error(f"加载配置失败: {str(e)}", exc_info=True)

    def load_exchange_config(self):
        """加载交易所特有配置"""
        try:
            # 获取策略配置
            self._init_configuration()

            logger.debug(f"加载 {self.exchange_name} 特有配置成功")

        except Exception as e:
            logger.error(f"加载 {self.exchange_name} 特有配置失败: {e}")

    async def stop(self):
        """停止策略"""
        self.is_running = False
        self._stop_event.set()
