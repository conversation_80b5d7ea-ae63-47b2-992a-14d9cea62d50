"""
策略管理器 - 管理多个交易所的资金费率套利策略
"""
import asyncio
import logging
from typing import Dict, Set

from crypt_carry.core.exchange.base.exchange_client import ExchangeClient
from crypt_carry.core.exchange.base.exchange_config import ExchangeConfig
from crypt_carry.core.exchange.factory.exchange_factory import ExchangeFactory
from crypt_carry.strategies.exchange_funding_strategy import ExchangeFundingStrategy

logger = logging.getLogger(__name__)


class StrategyManager:
    """策略管理器，管理多个交易所的资金费率套利策略"""

    def __init__(self):
        """初始化策略管理器"""
        self.config = ExchangeConfig()

        # 初始化交易所客户端和策略
        self.exchange_clients: Dict[str, ExchangeClient] = {}
        self.strategies: Dict[str, ExchangeFundingStrategy] = {}

        # 初始化交易所支持的币对集合
        self.exchange_symbols: Dict[str, Set[str]] = {}

        # 根据配置创建交易所客户端和策略
        for exchange_name in self.config.get_exchanges():
            # 检查交易所是否启用
            if not self.config.is_exchange_enabled(exchange_name):
                logger.info(f"交易所 {exchange_name} 未启用,跳过初始化")
                continue

            # 创建交易所客户端
            self.has_exchange_name_config = ExchangeConfig(exchange_name)
            self.config = self.has_exchange_name_config
            client = ExchangeFactory.create_exchange_client(exchange_name, self.config)
            if client is None:
                logger.warning(f"不支持的交易所: {exchange_name}")
                continue

            # 保存客户端并创建策略
            self.exchange_clients[exchange_name] = client
            self.strategies[exchange_name] = ExchangeFundingStrategy(client)

        enabled_exchanges = [name for name in self.config.get_exchanges() if self.config.is_exchange_enabled(name)]
        logger.info(f"初始化了 {len(enabled_exchanges)} 个已启用的交易所: {', '.join(enabled_exchanges)}")

    async def start(self):
        """启动策略"""
        try:
            # 启动所有交易所客户端
            exchange_tasks = []
            for exchange_name, client in self.exchange_clients.items():
                logger.info(f"正在启动 {exchange_name}...")
                exchange_tasks.append(client.start())

            # 等待所有交易所客户端启动完成
            results = await asyncio.gather(*exchange_tasks, return_exceptions=True)

            # 检查启动结果
            all_started = True
            for exchange_name, result in zip(self.exchange_clients.keys(), results):
                if isinstance(result, Exception):
                    logger.error(f"{exchange_name} 启动失败: {str(result)}")
                    all_started = False
                elif not result:
                    logger.error(f"{exchange_name} 启动失败")
                    all_started = False
                else:
                    logger.info(f"{exchange_name} 启动成功")

            if not all_started:
                logger.error("部分交易所启动失败，终止套利策略启动")
                return False

            # 所有交易所启动成功后，启动套利策略
            logger.info("所有交易所启动成功，开始启动套利策略...")
            return True

        except Exception as e:
            logger.error(f"策略启动失败: {str(e)}")
            return False

    async def run(self):
        """运行策略"""
        try:
            # 先启动所有组件
            if not await self.start():
                return

            # 创建每个交易所的策略运行任务
            strategy_tasks = []
            for exchange_name, strategy in self.strategies.items():
                strategy_tasks.append(asyncio.create_task(strategy.run()))

            # 等待所有策略任务完成
            await asyncio.gather(*strategy_tasks)

        except Exception as e:
            logger.error(f"策略运行失败: {str(e)}")

    async def stop(self):
        """停止策略"""
        try:
            # 停止所有策略
            for exchange_name, strategy in self.strategies.items():
                await strategy.stop()
                logger.info(f"{exchange_name} 策略已停止")

            # 停止所有交易所客户端
            for exchange_name, client in self.exchange_clients.items():
                await client.stop()
                logger.info(f"{exchange_name} 交易所客户端已停止")

            logger.info("所有交易所已停止")

        except Exception as e:
            logger.error(f"停止策略失败: {str(e)}")