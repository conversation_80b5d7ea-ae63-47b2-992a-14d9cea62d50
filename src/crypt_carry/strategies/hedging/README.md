# 跨交易所套保策略框架

## 📁 文件结构和职责

### 🎮 程序入口
**`hedging_manager.py`** - **这是唯一的程序入口**
- 负责创建和初始化所有组件
- 提供统一的API接口
- 管理组件的生命周期
- 协调各个组件的工作

### 🧩 核心组件

#### `cross_exchange_coordinator.py` - 跨交易所协调器
- 管理多个交易所的连接和状态
- 协调跨交易所的订单执行
- 处理网络延迟和同步问题
- 监控交易所状态和网络延迟

#### `hedging_strategy.py` - 套保策略引擎
- **纯策略逻辑，不负责组件初始化**
- 监控触发条件（价差、资金费率差异等）
- 执行多空套保逻辑
- 管理套保条件的生命周期
- 通过依赖注入接收其他组件

#### `risk_manager.py` - 风险管理器
- 监控跨交易所持仓风险
- 处理异常情况
- 实施止损和风控措施
- 提供风险指标和报告

#### `hedging_position_manager.py` - 套保持仓管理器
- 管理跨交易所的配对持仓
- 计算实时盈亏
- 处理持仓调整和平仓
- 支持自动止损止盈

## 🔄 架构设计原则

### 1. 单一入口原则
- **只有 `hedging_manager.py` 是程序入口**
- 其他组件都是被管理器创建和初始化的
- 避免重复初始化和资源冲突

### 2. 依赖注入原则
- `hedging_strategy.py` 通过构造函数接收其他组件
- 不自己创建依赖的组件
- 便于测试和模块解耦

### 3. 职责分离原则
- 每个组件只负责自己的核心功能
- 管理器负责协调，策略负责逻辑，组件负责执行

## 🚀 使用方式

### 基本使用流程

```python
from crypt_carry.strategies.hedging.hedging_manager import HedgingStrategyManager

# 1. 创建管理器（程序入口）
manager = HedgingStrategyManager(["binance", "okx"])

# 2. 初始化
await manager.initialize()

# 3. 添加套保条件
await manager.add_hedging_condition(
    condition_id="btc_price_diff",
    condition_type="price_difference",
    long_exchange="binance",
    long_symbol="BTC/USDT",
    short_exchange="okx",
    short_symbol="BTC/USDT",
    trigger_value=50.0,
    comparison_operator=">",
    amount_usdt=1000.0
)

# 4. 启动策略
await manager.start()

# 5. 清理资源
await manager.cleanup()
```

### 支持的套保条件类型

1. **价差套保** (`price_difference`)
   - 监控两个交易所同一交易对的价差
   - 当价差超过阈值时触发套保

2. **资金费率差异套保** (`funding_rate_diff`)
   - 监控两个交易所期货的资金费率差异
   - 当差异超过阈值时触发套保

3. **基差率套保** (`basis_rate`)
   - 监控现货和期货的基差率
   - 当基差率超过阈值时触发套保

4. **自定义条件** (`custom`)
   - 支持自定义的触发条件逻辑

## ⚠️ 重要说明

### 不要直接使用其他组件
❌ **错误的使用方式：**
```python
# 不要这样做！
coordinator = CrossExchangeCoordinator()
strategy = HedgingStrategy(["binance", "okx"])
await coordinator.initialize(["binance", "okx"])
await strategy.initialize()
```

✅ **正确的使用方式：**
```python
# 只使用管理器作为入口
manager = HedgingStrategyManager(["binance", "okx"])
await manager.initialize()
await manager.start()
```

### 组件关系图
```
HedgingStrategyManager (入口)
├── CrossExchangeCoordinator (交易所协调)
├── RiskManager (风险管理)
├── HedgingPositionManager (持仓管理)
└── HedgingStrategy (策略逻辑)
    ├── 依赖注入 → CrossExchangeCoordinator
    ├── 依赖注入 → RiskManager
    └── 依赖注入 → HedgingPositionManager
```

## 📝 示例代码

详细的使用示例请参考 `example_usage.py` 文件，其中包含：
- 完整的使用流程
- 各种套保条件的配置示例
- 状态监控和管理示例
- 错误处理示例

## 🔧 配置选项

```python
manager.update_config(
    max_positions=10,      # 最大持仓数量
    min_amount=100,        # 最小交易金额(USDT)
    max_amount=10000,      # 最大交易金额(USDT)
    check_interval=5,      # 检查间隔(秒)
    auto_start_monitoring=True  # 是否自动启动监控
)
```

## 🎯 扩展开发

如果需要添加新的功能：
1. 新的触发条件类型 → 修改 `hedging_strategy.py`
2. 新的风险控制逻辑 → 修改 `risk_manager.py`
3. 新的交易执行逻辑 → 修改 `cross_exchange_coordinator.py`
4. 新的持仓管理功能 → 修改 `hedging_position_manager.py`

所有修改都通过 `hedging_manager.py` 统一对外提供接口。
