"""
跨交易所套保策略管理器 - 统一管理套保策略的生命周期
"""
import asyncio
import logging
from typing import Dict, List, Optional
from datetime import datetime, timezone

from crypt_carry.strategies.hedging.hedging_strategy import HedgingStrategy, HedgingCondition, TriggerType
from crypt_carry.strategies.hedging.cross_exchange_coordinator import CrossExchangeCoordinator
from crypt_carry.strategies.hedging.risk_manager import RiskManager
from crypt_carry.strategies.hedging.hedging_position_manager import HedgingPositionManager
from crypt_carry.utils.ding_talk_manager import DingTalkManager
from crypt_carry.config.hedging_config_manager import HedgingConfigManager

logger = logging.getLogger(__name__)


class HedgingStrategyManager:
    """跨交易所套保策略管理器"""

    def __init__(self, exchange_names: Optional[List[str]] = None):
        """初始化套保策略管理器

        Args:
            exchange_names: 支持的交易所列表，如果为None则从配置文件读取
        """
        # 配置管理器
        self.config_manager = HedgingConfigManager()

        # 交易所列表（优先使用参数，否则从配置读取）
        self.exchange_names = exchange_names or self.config_manager.exchanges
        self.is_running = False

        # 核心组件
        self.hedging_strategy: Optional[HedgingStrategy] = None
        self.coordinator: Optional[CrossExchangeCoordinator] = None
        self.risk_manager: Optional[RiskManager] = None
        self.position_manager: Optional[HedgingPositionManager] = None

        # 钉钉消息管理器
        self.ding_talk_manager = DingTalkManager.get_instance()

    async def initialize(self):
        """初始化策略管理器"""
        try:
            logger.info("初始化跨交易所套保策略管理器...")

            # 创建核心组件
            self.coordinator = CrossExchangeCoordinator()
            self.risk_manager = RiskManager()
            self.position_manager = HedgingPositionManager()

            # 初始化所有组件
            await self.coordinator.initialize(self.exchange_names)
            await self.risk_manager.initialize()
            await self.position_manager.initialize()

            # 创建策略引擎（依赖注入）
            self.hedging_strategy = HedgingStrategy(
                coordinator=self.coordinator,
                risk_manager=self.risk_manager,
                position_manager=self.position_manager
            )


            logger.info("跨交易所套保策略管理器初始化完成")

        except Exception as e:
            logger.error(f"初始化套保策略管理器失败: {str(e)}")
            raise


    async def start(self):
        """启动策略管理器"""
        try:
            if self.is_running:
                logger.warning("策略管理器已在运行")
                return

            self.is_running = True
            logger.info("启动跨交易所套保策略管理器")

            # 启动各个组件的监控
            tasks = []

            if self.config_manager.auto_start_monitoring:
                # 启动风险监控
                if self.risk_manager:
                    tasks.append(asyncio.create_task(self.risk_manager.start_monitoring()))

                # 启动持仓监控
                if self.position_manager:
                    tasks.append(asyncio.create_task(self.position_manager.start_monitoring()))

                # 启动协调器监控
                if self.coordinator:
                    tasks.append(asyncio.create_task(self.coordinator.start_monitoring()))

            # 启动主策略
            if self.hedging_strategy:
                tasks.append(asyncio.create_task(self.hedging_strategy.start()))

            # 发送启动通知
            message = (
                f"跨交易所套保策略管理器已启动\n"
                f"支持交易所: {', '.join(self.exchange_names)}\n"
                f"最大持仓数: {self.config_manager.max_positions}\n"
                f"交易金额范围: {self.config_manager.min_amount}-{self.config_manager.max_amount} USDT"
            )
            self.ding_talk_manager.send_message(message, prefix="套保管理器")

            # 等待所有任务完成
            if tasks:
                await asyncio.gather(*tasks, return_exceptions=True)

        except Exception as e:
            logger.error(f"启动策略管理器失败: {str(e)}")
            self.is_running = False
        finally:
            self.is_running = False

    async def stop(self):
        """停止策略管理器"""
        try:
            logger.info("停止跨交易所套保策略管理器")
            self.is_running = False

            # 停止所有组件
            if self.hedging_strategy:
                await self.hedging_strategy.stop()

            if self.risk_manager:
                await self.risk_manager.stop_monitoring()

            if self.position_manager:
                await self.position_manager.stop_monitoring()

            if self.coordinator:
                await self.coordinator.stop_monitoring()

            # 发送停止通知
            message = "跨交易所套保策略管理器已停止"
            self.ding_talk_manager.send_message(message, prefix="套保管理器")

        except Exception as e:
            logger.error(f"停止策略管理器失败: {str(e)}")

    async def add_hedging_condition(self,
                                   condition_id: str,
                                   condition_type: str,
                                   long_exchange: str,
                                   long_symbol: str,
                                   short_exchange: str,
                                   short_symbol: str,
                                   trigger_value: float,
                                   comparison_operator: str,
                                   amount: float,
                                   amount_currency: str,
                                   long_is_spot: bool = True,
                                   short_is_spot: bool = False,
                                   priority: int = 1) -> bool:
        """添加套保条件

        Args:
            condition_id: 条件ID
            condition_type: 条件类型
            long_exchange: 多头交易所
            long_symbol: 多头交易对
            short_exchange: 空头交易所
            short_symbol: 空头交易对
            trigger_value: 触发值
            comparison_operator: 比较运算符
            amount: 交易金额
            amount_currency: 交易金额币种 (USDT/USDC等)
            long_is_spot: 多头是否为现货
            short_is_spot: 空头是否为现货
            priority: 优先级

        Returns:
            bool: 是否添加成功
        """
        try:
            if not self.hedging_strategy:
                logger.error("套保策略未初始化")
                return False

            # 转换条件类型
            trigger_type_map = {
                'price_difference': TriggerType.PRICE_DIFFERENCE,
                'funding_rate_diff': TriggerType.FUNDING_RATE_DIFF,
                'basis_rate': TriggerType.BASIS_RATE,
                'custom': TriggerType.CUSTOM
            }

            trigger_type = trigger_type_map.get(condition_type)
            if not trigger_type:
                logger.error(f"不支持的条件类型: {condition_type}")
                return False

            # 创建套保条件
            condition = HedgingCondition(
                condition_id=condition_id,
                condition_type=trigger_type,
                long_exchange=long_exchange,
                long_symbol=long_symbol,
                long_is_spot=long_is_spot,
                short_exchange=short_exchange,
                short_symbol=short_symbol,
                short_is_spot=short_is_spot,
                trigger_value=trigger_value,
                comparison_operator=comparison_operator,
                amount=amount,
                amount_currency=amount_currency,
                priority=priority,
                created_at=datetime.now(timezone.utc)
            )

            # 添加到策略
            success = await self.hedging_strategy.add_condition(condition)

            if success:
                logger.info(f"成功添加套保条件: {condition_id}")

                # 发送通知
                message = (
                    f"新增套保条件\n"
                    f"条件ID: {condition_id}\n"
                    f"类型: {condition_type}\n"
                    f"多头: {long_exchange} {long_symbol}\n"
                    f"空头: {short_exchange} {short_symbol}\n"
                    f"触发值: {trigger_value}\n"
                    f"金额: {amount} {amount_currency}"
                )
                self.ding_talk_manager.send_message(message, prefix="套保条件")

            return success

        except Exception as e:
            logger.error(f"添加套保条件失败: {str(e)}")
            return False

    async def remove_hedging_condition(self, condition_id: str) -> bool:
        """移除套保条件

        Args:
            condition_id: 条件ID

        Returns:
            bool: 是否移除成功
        """
        try:
            if not self.hedging_strategy:
                logger.error("套保策略未初始化")
                return False

            # 移除条件
            success = await self.hedging_strategy.remove_condition(condition_id)

            if success:
                logger.info(f"成功移除套保条件: {condition_id}")

                # 发送通知
                message = f"移除套保条件\n条件ID: {condition_id}"
                self.ding_talk_manager.send_message(message, prefix="套保条件")

            return success

        except Exception as e:
            logger.error(f"移除套保条件失败: {str(e)}")
            return False

    async def execute_immediate_hedging(self,
                                       long_exchange: str,
                                       long_symbol: str,
                                       short_exchange: str,
                                       short_symbol: str,
                                       amount: float,
                                       amount_currency: str,
                                       long_is_spot: bool = True,
                                       short_is_spot: bool = False) -> dict:
        """立即执行套保操作（无触发条件）

        Args:
            long_exchange: 多头交易所
            long_symbol: 多头交易对
            short_exchange: 空头交易所
            short_symbol: 空头交易对
            amount: 交易金额
            amount_currency: 交易金额币种
            long_is_spot: 多头是否为现货
            short_is_spot: 空头是否为现货

        Returns:
            dict: 执行结果
        """
        try:
            logger.info(f"开始立即执行套保: {long_exchange} {long_symbol} vs {short_exchange} {short_symbol}")

            # 验证交易金额
            if amount <= 0:
                logger.error(f"无效的交易金额: {amount}")
                return {'success': False, 'error': '无效的交易金额'}

            # 验证交易币种
            if not amount_currency:
                logger.error("交易币种不能为空")
                return {'success': False, 'error': '交易币种不能为空'}

            # 转换为USDT等价值进行验证
            amount_usdt_equivalent = amount
            if amount_currency != 'USDT':
                if amount_currency == 'USDC':
                    amount_usdt_equivalent = amount  # 1:1假设
                else:
                    logger.warning(f"暂不支持的交易币种: {amount_currency}")
                    return {'success': False, 'error': f'暂不支持的交易币种: {amount_currency}'}

            # 验证金额范围
            if hasattr(self.hedging_strategy, 'min_amount') and hasattr(self.hedging_strategy, 'max_amount'):
                if amount_usdt_equivalent < self.hedging_strategy.min_amount or amount_usdt_equivalent > self.hedging_strategy.max_amount:
                    error_msg = f"交易金额超出限制: {amount_usdt_equivalent} (范围: {self.hedging_strategy.min_amount}-{self.hedging_strategy.max_amount})"
                    logger.warning(error_msg)
                    return {'success': False, 'error': error_msg}

            # 执行跨交易所套保
            if hasattr(self, 'coordinator') and self.coordinator:
                hedging_pair = await self.coordinator.execute_hedging_pair(
                    long_exchange=long_exchange,
                    long_symbol=long_symbol,
                    short_exchange=short_exchange,
                    short_symbol=short_symbol,
                    amount=amount,
                    amount_currency=amount_currency,
                    long_is_spot=long_is_spot,
                    short_is_spot=short_is_spot
                )

                if hedging_pair:
                    # 发送成功通知
                    message = (
                        f"立即套保执行成功\n"
                        f"多头: {long_exchange} {long_symbol}\n"
                        f"空头: {short_exchange} {short_symbol}\n"
                        f"金额: {amount} {amount_currency}"
                    )
                    self.ding_talk_manager.send_message(message, prefix="立即套保")

                    return {
                        'success': True,
                        'message': '立即套保执行成功',
                        'hedging_pair': hedging_pair,
                        'executed_at': datetime.now(timezone.utc).isoformat()
                    }
                else:
                    return {'success': False, 'error': '套保执行失败'}
            else:
                # 模拟执行
                logger.info("协调器未初始化，使用模拟模式")
                return {
                    'success': True,
                    'message': '立即套保执行成功 (模拟模式)',
                    'hedging_pair': {
                        'long_order': {'exchange': long_exchange, 'symbol': long_symbol, 'status': 'simulated'},
                        'short_order': {'exchange': short_exchange, 'symbol': short_symbol, 'status': 'simulated'}
                    },
                    'executed_at': datetime.now(timezone.utc).isoformat(),
                    'note': '模拟模式 - 未实际执行交易'
                }

        except Exception as e:
            logger.error(f"立即执行套保失败: {str(e)}")
            return {'success': False, 'error': f'立即执行套保失败: {str(e)}'}

# 重复的方法已删除

    async def close_position(self, pair_id: str, reason: str = "手动平仓") -> bool:
        """平仓指定持仓

        Args:
            pair_id: 配对ID
            reason: 平仓原因

        Returns:
            bool: 是否平仓成功
        """
        try:
            if not self.position_manager:
                logger.error("持仓管理器未初始化")
                return False

            return await self.position_manager.close_position(pair_id, reason)

        except Exception as e:
            logger.error(f"平仓失败: {str(e)}")
            return False

    def get_status(self) -> Dict:
        """获取策略管理器状态"""
        try:
            status = {
                'is_running': self.is_running,
                'exchange_names': self.exchange_names,
                'config': self.config_manager.get_config_summary(),
                'components': {
                    'hedging_strategy': self.hedging_strategy.get_status() if self.hedging_strategy else None,
                    'risk_manager': self.risk_manager.get_risk_summary() if self.risk_manager else None,
                    'position_manager': self.position_manager.get_position_summary() if self.position_manager else None,
                    'coordinator': self.coordinator.get_exchange_status() if self.coordinator else None
                }
            }

            return status

        except Exception as e:
            logger.error(f"获取状态失败: {str(e)}")
            return {'error': str(e)}

    def get_conditions(self) -> Dict:
        """获取所有套保条件"""
        try:
            if not self.hedging_strategy:
                return {}

            conditions = self.hedging_strategy.get_conditions()

            # 转换为可序列化的格式
            result = {}
            for condition_id, condition in conditions.items():
                result[condition_id] = {
                    'condition_id': condition.condition_id,
                    'condition_type': condition.condition_type.value,
                    'long_exchange': condition.long_exchange,
                    'long_symbol': condition.long_symbol,
                    'long_is_spot': condition.long_is_spot,
                    'short_exchange': condition.short_exchange,
                    'short_symbol': condition.short_symbol,
                    'short_is_spot': condition.short_is_spot,
                    'trigger_value': condition.trigger_value,
                    'comparison_operator': condition.comparison_operator,
                    'amount': condition.amount,
                    'amount_currency': condition.amount_currency,
                    'is_active': condition.is_active,
                    'priority': condition.priority,
                    'created_at': condition.created_at.isoformat() if condition.created_at else None
                }

            return result

        except Exception as e:
            logger.error(f"获取套保条件失败: {str(e)}")
            return {}

    def get_positions(self) -> Dict:
        """获取所有持仓"""
        try:
            if not self.position_manager:
                return {}

            return self.position_manager.get_position_summary()

        except Exception as e:
            logger.error(f"获取持仓失败: {str(e)}")
            return {}

    async def cleanup(self):
        """清理资源"""
        try:
            # 停止策略管理器
            await self.stop()

            # 清理策略引擎
            if self.hedging_strategy:
                await self.hedging_strategy.cleanup()

            # 清理核心组件
            if self.coordinator:
                await self.coordinator.cleanup()

            if self.risk_manager:
                await self.risk_manager.cleanup()

            if self.position_manager:
                await self.position_manager.cleanup()

            logger.info("跨交易所套保策略管理器清理完成")

        except Exception as e:
            logger.error(f"清理策略管理器失败: {str(e)}")
