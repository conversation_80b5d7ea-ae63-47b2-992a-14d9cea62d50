"""
订单处理器 - 统一管理订单的完整生命周期
负责订单的创建、拆分、执行流程控制，实现单一职责原则
"""
import asyncio
import logging
from typing import Dict, Any
from enum import Enum

from .cache_manager import HedgingOrder, OrderStatus, ExecutionType, get_order_cache_manager
from .splitter import get_order_splitter
from .executor import get_task_executor

logger = logging.getLogger(__name__)


class ProcessingStage(Enum):
    """订单处理阶段"""
    CREATED = "created"           # 已创建
    VALIDATING = "validating"     # 验证中
    WAITING = "waiting"           # 等待条件
    SPLITTING = "splitting"       # 拆单中
    EXECUTING = "executing"       # 执行中
    COMPLETED = "completed"       # 已完成
    FAILED = "failed"            # 失败


class OrderProcessor:
    """订单处理器 - 统一管理订单生命周期"""

    def __init__(self):
        """初始化订单处理器"""
        self.order_cache = get_order_cache_manager()
        self.order_splitter = get_order_splitter()
        self.task_executor = get_task_executor()
        self.is_running = False

    async def start(self):
        """启动订单处理器"""
        if not self.is_running:
            self.is_running = True
            # 启动任务执行器
            if not self.task_executor.is_running:
                await self.task_executor.start()
            logger.info("订单处理器已启动")

    async def stop(self):
        """停止订单处理器"""
        if self.is_running:
            self.is_running = False
            logger.info("订单处理器已停止")

    async def process_order(self, order: HedgingOrder) -> Dict[str, Any]:
        """处理订单 - 主入口方法

        Args:
            order: 套保订单对象

        Returns:
            Dict[str, Any]: 处理结果
        """
        try:
            logger.info(f"订单处理器开始处理订单: {order.order_id}")

            # 1. 验证订单
            validation_result = await self._validate_order(order)
            if not validation_result['success']:
                await self._update_processing_stage(order.order_id, ProcessingStage.FAILED)
                return validation_result

            # 2. 根据执行类型选择处理路径
            if order.execution_type == ExecutionType.IMMEDIATE:
                # 立即执行：直接进入拆单流程
                return await self._process_immediate_order(order)
            else:
                # 条件执行：等待策略引擎触发
                return await self._process_conditional_order(order)

        except Exception as e:
            logger.error(f"订单处理失败: {order.order_id}, 错误: {str(e)}")
            await self._update_processing_stage(order.order_id, ProcessingStage.FAILED)
            return {
                'success': False,
                'error': f'订单处理失败: {str(e)}'
            }

    async def _validate_order(self, order: HedgingOrder) -> Dict[str, Any]:
        """验证订单"""
        try:
            await self._update_processing_stage(order.order_id, ProcessingStage.VALIDATING)

            # 基本验证
            if not order.long_exchange or not order.short_exchange:
                return {'success': False, 'error': '交易所信息不完整'}

            if not order.long_symbol or not order.short_symbol:
                return {'success': False, 'error': '交易对信息不完整'}

            if order.amount <= 0:
                return {'success': False, 'error': '交易金额必须大于0'}

            # TODO: 添加更多验证逻辑（余额检查、交易对有效性等）

            logger.info(f"订单验证通过: {order.order_id}")
            return {'success': True}

        except Exception as e:
            logger.error(f"订单验证失败: {order.order_id}, 错误: {str(e)}")
            return {'success': False, 'error': f'订单验证失败: {str(e)}'}

    async def _process_immediate_order(self, order: HedgingOrder) -> Dict[str, Any]:
        """处理立即执行订单"""
        try:
            logger.info(f"处理立即执行订单: {order.order_id}")

            # 启动后台拆单和执行流程
            asyncio.create_task(self._split_and_execute_order(order))

            return {
                'success': True,
                'message': '立即执行订单已提交后台处理',
                'processing_stage': ProcessingStage.SPLITTING.value
            }

        except Exception as e:
            logger.error(f"处理立即执行订单失败: {order.order_id}, 错误: {str(e)}")
            return {'success': False, 'error': f'处理失败: {str(e)}'}

    async def _process_conditional_order(self, order: HedgingOrder) -> Dict[str, Any]:
        """处理条件执行订单"""
        try:
            logger.info(f"处理条件执行订单: {order.order_id}, 条件数量: {len(order.conditions)}")

            # 更新状态为等待条件
            await self._update_processing_stage(order.order_id, ProcessingStage.WAITING)

            # 委托给策略引擎进行条件监控（单一职责原则）
            # 订单处理器不负责条件监控，只负责订单生命周期管理
            # 策略引擎会通过监控循环发现等待状态的订单并添加到监控队列

            logger.info(f"条件执行订单已准备就绪，等待策略引擎添加到监控队列: {order.order_id}")

            return {
                'success': True,
                'message': '条件执行订单已准备就绪，等待策略引擎监控',
                'processing_stage': ProcessingStage.WAITING.value,
                'conditions_count': len(order.conditions)
            }

        except Exception as e:
            logger.error(f"处理条件执行订单失败: {order.order_id}, 错误: {str(e)}")
            return {'success': False, 'error': f'处理失败: {str(e)}'}

    async def trigger_conditional_order(self, order_id: str) -> Dict[str, Any]:
        """触发条件执行订单（由策略引擎调用）

        Args:
            order_id: 订单ID

        Returns:
            Dict[str, Any]: 触发结果
        """
        try:
            logger.info(f"策略引擎触发条件执行订单: {order_id}")

            # 获取订单
            order = await self.order_cache.get_order(order_id)
            if not order:
                return {'success': False, 'error': f'订单不存在: {order_id}'}

            # 检查订单状态
            if order.status != OrderStatus.WAITING:
                return {'success': False, 'error': f'订单状态不正确: {order.status}'}

            # 启动拆单和执行流程
            asyncio.create_task(self._split_and_execute_order(order))

            return {
                'success': True,
                'message': f'条件执行订单已触发: {order_id}',
                'processing_stage': ProcessingStage.SPLITTING.value
            }

        except Exception as e:
            logger.error(f"触发条件执行订单失败: {order_id}, 错误: {str(e)}")
            return {'success': False, 'error': f'触发失败: {str(e)}'}

    async def _split_and_execute_order(self, order: HedgingOrder):
        """拆分并执行订单（后台任务）

        Args:
            order: 套保订单对象
        """
        try:
            logger.info(f"开始拆分并执行订单: {order.order_id}")

            # 1. 更新状态为拆单中
            await self._update_processing_stage(order.order_id, ProcessingStage.SPLITTING)
            await self.order_cache.update_order_status(order.order_id, OrderStatus.SPLITTING)

            # 2. 进行智能拆单
            split_tasks = await self.order_splitter.split_order(order)

            if not split_tasks:
                logger.error(f"拆单失败: {order.order_id}")
                await self._update_processing_stage(order.order_id, ProcessingStage.FAILED)
                await self.order_cache.update_order_status(order.order_id, OrderStatus.FAILED)
                return

            # 3. 保存拆单任务
            await self.order_cache.save_split_tasks(order.order_id, split_tasks)
            logger.info(f"订单拆分完成: {order.order_id}, 拆分为 {len(split_tasks)} 个任务")

            # 4. 更新状态为执行中
            await self._update_processing_stage(order.order_id, ProcessingStage.EXECUTING)
            await self.order_cache.update_order_status(order.order_id, OrderStatus.EXECUTING)

            # 5. 任务执行器会自动轮询执行拆分的任务
            # 这里不需要手动触发，保持模块职责单一

            logger.info(f"订单处理流程完成: {order.order_id}, 任务已提交给执行器")

        except Exception as e:
            logger.error(f"拆分并执行订单失败: {order.order_id}, 错误: {str(e)}")
            await self._update_processing_stage(order.order_id, ProcessingStage.FAILED)
            await self.order_cache.update_order_status(order.order_id, OrderStatus.FAILED)

    async def _update_processing_stage(self, order_id: str, stage: ProcessingStage):
        """更新订单处理阶段"""
        try:
            # 这里可以扩展为更详细的阶段跟踪
            logger.debug(f"订单 {order_id} 进入阶段: {stage.value}")
        except Exception as e:
            logger.error(f"更新处理阶段失败: {order_id}, 错误: {str(e)}")

    async def get_processing_status(self) -> Dict[str, Any]:
        """获取处理器状态"""
        try:
            return {
                'is_running': self.is_running,
                'task_executor_status': await self.task_executor.get_execution_status(),
                'cache_status': 'active'  # 可以扩展更多状态信息
            }
        except Exception as e:
            logger.error(f"获取处理器状态失败: {str(e)}")
            return {'error': str(e)}


# 全局单例
_order_processor = None

def get_order_processor() -> OrderProcessor:
    """获取订单处理器单例"""
    global _order_processor
    if _order_processor is None:
        _order_processor = OrderProcessor()
    return _order_processor
