"""
智能拆单管理器
根据订单金额和市场情况进行智能拆单，减少滑点和磨损
"""
import asyncio
import logging
import math
from datetime import datetime, timezone
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass

from .cache_manager import HedgingOrder, SplitTask

logger = logging.getLogger(__name__)


@dataclass
class SplitConfig:
    """拆单配置"""
    # 基础配置
    min_split_amount: float = 100.0      # 最小拆单金额(USDT)
    max_split_amount: float = 2000.0     # 最大拆单金额(USDT)
    max_splits_per_order: int = 20       # 每个订单最大拆单数量

    # 动态拆单配置
    small_order_threshold: float = 500.0   # 小订单阈值(USDT)
    medium_order_threshold: float = 2000.0 # 中等订单阈值(USDT)
    large_order_threshold: float = 10000.0 # 大订单阈值(USDT)

    # 拆单策略
    small_order_splits: int = 1          # 小订单拆单数
    medium_order_splits: int = 4         # 中等订单拆单数
    large_order_splits: int = 10          # 大订单拆单数
    xlarge_order_splits: int = 12         # 超大订单拆单数


class OrderSplitter:
    """智能拆单管理器"""

    def __init__(self, config: Optional[SplitConfig] = None):
        """初始化拆单管理器

        Args:
            config: 拆单配置
        """
        self.config = config or SplitConfig()
        logger.info("智能拆单管理器初始化完成")

    async def split_order(self, order: HedgingOrder) -> List[SplitTask]:
        """智能拆单

        Args:
            order: 套保订单

        Returns:
            List[SplitTask]: 拆单任务列表
        """
        try:
            logger.info(f"开始拆单: {order.order_id}, 金额: {order.amount} {order.amount_currency}")

            # 转换为USDT等价值
            amount_usdt = await self._convert_to_usdt(order.amount, order.amount_currency)

            # 计算拆单数量
            split_count = await self._calculate_split_count(amount_usdt)

            # 生成拆单任务
            split_tasks = await self._generate_split_tasks(order, split_count, amount_usdt)

            logger.info(f"拆单完成: {order.order_id}, 拆分为 {len(split_tasks)} 个任务")
            return split_tasks

        except Exception as e:
            logger.error(f"拆单失败: {str(e)}")
            return []

    async def _convert_to_usdt(self, amount: float, currency: str) -> float:
        """转换为USDT等价值

        Args:
            amount: 金额
            currency: 币种

        Returns:
            float: USDT等价值
        """
        try:
            if currency == 'USDT':
                return amount
            elif currency == 'USDC':
                return amount  # 1:1假设
            else:
                # TODO: 实现其他币种的汇率转换
                logger.warning(f"暂不支持的币种: {currency}, 使用1:1转换")
                return amount

        except Exception as e:
            logger.error(f"币种转换失败: {str(e)}")
            return amount

    async def _calculate_split_count(self, amount_usdt: float) -> int:
        """计算拆单数量

        Args:
            amount_usdt: USDT金额

        Returns:
            int: 拆单数量
        """
        try:
            # 根据金额大小确定拆单策略
            if amount_usdt <= self.config.small_order_threshold:
                # 小订单：不拆单或少拆单
                split_count = self.config.small_order_splits
            elif amount_usdt <= self.config.medium_order_threshold:
                # 中等订单：适度拆单
                split_count = self.config.medium_order_splits
            elif amount_usdt <= self.config.large_order_threshold:
                # 大订单：较多拆单
                split_count = self.config.large_order_splits
            else:
                # 超大订单：最多拆单
                split_count = self.config.xlarge_order_splits

            # 动态调整：确保每个拆单不会太小或太大
            min_splits = max(1, math.ceil(amount_usdt / self.config.max_split_amount))
            max_splits = min(self.config.max_splits_per_order,
                           math.floor(amount_usdt / self.config.min_split_amount))

            split_count = max(min_splits, min(split_count, max_splits))

            logger.debug(f"拆单数量计算: 金额={amount_usdt}, 拆单数={split_count}")
            return split_count

        except Exception as e:
            logger.error(f"计算拆单数量失败: {str(e)}")
            return 1

    async def _generate_split_tasks(self, order: HedgingOrder, split_count: int,
                                  total_amount_usdt: float) -> List[SplitTask]:
        """生成拆单任务

        Args:
            order: 原始订单
            split_count: 拆单数量
            total_amount_usdt: 总金额(USDT)

        Returns:
            List[SplitTask]: 拆单任务列表
        """
        try:
            split_tasks = []

            # 计算每个拆单的基础金额
            base_amount = total_amount_usdt / split_count

            # 生成拆单任务
            for i in range(split_count):
                # 计算当前拆单的金额
                if i == split_count - 1:
                    # 最后一个拆单包含所有剩余金额，避免精度问题
                    current_amount_usdt = total_amount_usdt - (base_amount * i)
                else:
                    current_amount_usdt = base_amount

                # 转换回原始币种
                current_amount = await self._convert_from_usdt(
                    current_amount_usdt, order.amount_currency
                )

                # 创建拆单任务
                task_id = f"{order.order_id}_split_{i+1}"
                split_task = SplitTask(
                    task_id=task_id,
                    order_id=order.order_id,
                    split_index=i + 1,
                    long_exchange=order.long_exchange,
                    long_symbol=order.long_symbol,
                    long_is_spot=order.long_is_spot,
                    short_exchange=order.short_exchange,
                    short_symbol=order.short_symbol,
                    short_is_spot=order.short_is_spot,
                    amount=current_amount,
                    amount_currency=order.amount_currency,
                    status="pending",
                    created_at=datetime.now(timezone.utc)
                )

                split_tasks.append(split_task)

                logger.debug(f"生成拆单任务: {task_id}, 金额: {current_amount:.2f} {order.amount_currency}")

            return split_tasks

        except Exception as e:
            logger.error(f"生成拆单任务失败: {str(e)}")
            return []

    async def _convert_from_usdt(self, amount_usdt: float, target_currency: str) -> float:
        """从USDT转换为目标币种

        Args:
            amount_usdt: USDT金额
            target_currency: 目标币种

        Returns:
            float: 目标币种金额
        """
        try:
            if target_currency == 'USDT':
                return amount_usdt
            elif target_currency == 'USDC':
                return amount_usdt  # 1:1假设
            else:
                # TODO: 实现其他币种的汇率转换
                logger.warning(f"暂不支持的币种: {target_currency}, 使用1:1转换")
                return amount_usdt

        except Exception as e:
            logger.error(f"币种转换失败: {str(e)}")
            return amount_usdt

    async def optimize_split_timing(self, split_tasks: List[SplitTask]) -> List[SplitTask]:
        """优化拆单时机

        根据市场情况和交易所特点优化拆单的执行顺序和时机

        Args:
            split_tasks: 原始拆单任务列表

        Returns:
            List[SplitTask]: 优化后的拆单任务列表
        """
        try:
            logger.info(f"开始优化拆单时机，任务数量: {len(split_tasks)}")

            # 当前简单实现：按金额从小到大排序
            # 先执行小额订单，测试市场反应
            optimized_tasks = sorted(split_tasks, key=lambda x: x.amount)

            # TODO: 可以添加更复杂的优化逻辑：
            # 1. 根据交易所流动性排序
            # 2. 根据历史滑点数据调整
            # 3. 根据市场波动性调整时间间隔
            # 4. 考虑交易所的maker/taker费率

            logger.info("拆单时机优化完成")
            return optimized_tasks

        except Exception as e:
            logger.error(f"优化拆单时机失败: {str(e)}")
            return split_tasks

    async def calculate_expected_slippage(self, split_tasks: List[SplitTask]) -> Dict[str, float]:
        """计算预期滑点

        Args:
            split_tasks: 拆单任务列表

        Returns:
            Dict[str, float]: 预期滑点信息
        """
        try:
            total_amount = sum(task.amount for task in split_tasks)
            avg_split_amount = total_amount / len(split_tasks) if split_tasks else 0

            # 简单的滑点估算模型
            # TODO: 可以基于历史数据和市场深度进行更精确的估算
            base_slippage = 0.001  # 0.1% 基础滑点
            size_factor = min(avg_split_amount / 1000, 0.005)  # 规模因子

            expected_slippage = base_slippage + size_factor

            return {
                'total_amount': total_amount,
                'split_count': len(split_tasks),
                'avg_split_amount': avg_split_amount,
                'expected_slippage_pct': expected_slippage * 100,
                'estimated_slippage_cost': total_amount * expected_slippage
            }

        except Exception as e:
            logger.error(f"计算预期滑点失败: {str(e)}")
            return {}

    async def get_split_summary(self, order: HedgingOrder, split_tasks: List[SplitTask]) -> Dict:
        """获取拆单摘要

        Args:
            order: 原始订单
            split_tasks: 拆单任务列表

        Returns:
            Dict: 拆单摘要信息
        """
        try:
            slippage_info = await self.calculate_expected_slippage(split_tasks)

            return {
                'order_id': order.order_id,
                'original_amount': order.amount,
                'amount_currency': order.amount_currency,
                'split_count': len(split_tasks),
                'split_amounts': [task.amount for task in split_tasks],
                'slippage_analysis': slippage_info,
                'split_strategy': self._get_split_strategy_name(order.amount),
                'created_at': datetime.now(timezone.utc).isoformat()
            }

        except Exception as e:
            logger.error(f"获取拆单摘要失败: {str(e)}")
            return {}

    def _get_split_strategy_name(self, amount: float) -> str:
        """获取拆单策略名称

        Args:
            amount: 订单金额

        Returns:
            str: 策略名称
        """
        if amount <= self.config.small_order_threshold:
            return "小订单策略"
        elif amount <= self.config.medium_order_threshold:
            return "中等订单策略"
        elif amount <= self.config.large_order_threshold:
            return "大订单策略"
        else:
            return "超大订单策略"


# 全局单例
_order_splitter = None

def get_order_splitter() -> OrderSplitter:
    """获取拆单管理器单例"""
    global _order_splitter
    if _order_splitter is None:
        _order_splitter = OrderSplitter()
    return _order_splitter
