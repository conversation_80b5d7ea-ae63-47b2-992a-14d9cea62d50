"""
套保策略配置使用示例
"""
import asyncio
import logging

from crypt_carry.config.hedging_config_manager import HedgingConfigManager
from crypt_carry.strategies.hedging.hedging_manager import HedgingStrategyManager

logger = logging.getLogger(__name__)


async def config_example():
    """配置使用示例"""
    
    # 1. 直接使用配置管理器
    config_manager = HedgingConfigManager()
    
    # 查看当前配置
    print("=== 当前配置摘要 ===")
    summary = config_manager.get_config_summary()
    for key, value in summary.items():
        print(f"{key}: {value}")
    
    # 2. 获取具体配置值
    print(f"\n=== 具体配置值 ===")
    print(f"支持的交易所: {config_manager.exchanges}")
    print(f"最大持仓数: {config_manager.max_positions}")
    print(f"交易金额范围: {config_manager.min_amount} - {config_manager.max_amount} USDT")
    print(f"止损阈值: {config_manager.stop_loss_threshold:.1%}")
    print(f"止盈阈值: {config_manager.take_profit_threshold:.1%}")
    
    # 3. 获取模板配置
    print(f"\n=== 模板配置 ===")
    btc_template = config_manager.get_template("PRICE_DIFFERENCE", "BTC_USDT")
    print(f"BTC价差模板: {btc_template}")
    
    default_template = config_manager.get_template("FUNDING_RATE_DIFF")
    print(f"资金费率差异默认模板: {default_template}")
    
    # 4. 更新配置
    print(f"\n=== 更新配置 ===")
    config_manager.update_strategy_config(
        MAX_POSITIONS=15,
        MIN_AMOUNT=200
    )
    print("策略配置已更新")
    
    config_manager.update_risk_config(
        STOP_LOSS_THRESHOLD=-0.08,
        TAKE_PROFIT_THRESHOLD=0.15
    )
    print("风险配置已更新")
    
    # 5. 重新加载配置
    config_manager.reload_config()
    print("配置已重新加载")


async def template_usage_example():
    """模板使用示例"""
    
    config_manager = HedgingConfigManager()
    manager = HedgingStrategyManager()
    
    await manager.initialize()
    
    # 使用模板创建套保条件
    btc_template = config_manager.get_template("PRICE_DIFFERENCE", "BTC_USDT")
    
    if btc_template:
        await manager.add_hedging_condition(
            condition_id="btc_template_hedge",
            condition_type="price_difference",
            long_exchange="binance",
            long_symbol="BTC/USDT",
            short_exchange="okx",
            short_symbol="BTC/USDT",
            trigger_value=btc_template.get("TRIGGER_VALUE", 100.0),
            comparison_operator=">",
            amount_usdt=btc_template.get("AMOUNT_USDT", 1000.0)
        )
        print("使用BTC模板创建套保条件成功")
    
    # 使用默认模板
    default_template = config_manager.get_template("FUNDING_RATE_DIFF")
    
    if default_template:
        await manager.add_hedging_condition(
            condition_id="funding_template_hedge",
            condition_type="funding_rate_diff",
            long_exchange="binance",
            long_symbol="ETH/USDT",
            short_exchange="okx",
            short_symbol="ETH/USDT",
            trigger_value=default_template.get("TRIGGER_VALUE", 0.001),
            comparison_operator=">",
            amount_usdt=default_template.get("AMOUNT_USDT", 1000.0),
            long_is_spot=False,
            short_is_spot=False
        )
        print("使用默认模板创建资金费率套保条件成功")
    
    # 查看创建的条件
    conditions = manager.get_conditions()
    print(f"已创建 {len(conditions)} 个套保条件")
    
    await manager.cleanup()


async def config_file_example():
    """配置文件使用示例"""
    
    print("=== 配置文件示例 ===")
    print("配置文件位置: src/crypt_carry/config/hedging_config.yaml")
    print()
    print("你可以直接编辑配置文件来修改设置：")
    print()
    print("STRATEGY:")
    print("  MAX_POSITIONS: 20")
    print("  MIN_AMOUNT: 500")
    print("  MAX_AMOUNT: 20000")
    print()
    print("RISK:")
    print("  STOP_LOSS_THRESHOLD: -0.05")
    print("  TAKE_PROFIT_THRESHOLD: 0.25")
    print()
    print("TEMPLATES:")
    print("  PRICE_DIFFERENCE:")
    print("    BTC_USDT:")
    print("      TRIGGER_VALUE: 200.0")
    print("      AMOUNT_USDT: 5000.0")
    print()
    print("修改后重启程序或调用 reload_config() 即可生效")


if __name__ == "__main__":
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    print("=== 套保策略配置使用示例 ===\n")
    
    # 运行配置示例
    asyncio.run(config_example())
    
    print("\n" + "="*50 + "\n")
    
    # 运行模板使用示例
    asyncio.run(template_usage_example())
    
    print("\n" + "="*50 + "\n")
    
    # 显示配置文件示例
    asyncio.run(config_file_example())
