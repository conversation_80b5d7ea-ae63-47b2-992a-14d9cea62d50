"""
跨交易所套保策略引擎 - 监控触发条件并执行多空套保逻辑
"""
import asyncio
import logging
from datetime import datetime, timezone
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

from crypt_carry.strategies.base_strategy import BaseStrategy
from crypt_carry.strategies.hedging.cross_exchange_coordinator import CrossExchangeCoordinator
from crypt_carry.strategies.hedging.risk_manager import RiskManager
from crypt_carry.strategies.hedging.hedging_position_manager import HedgingPositionManager
from crypt_carry.core.exchange.market.market_data_manager import MarketDataManager
from crypt_carry.utils.ding_talk_manager import DingTalkManager

logger = logging.getLogger(__name__)


class TriggerType(Enum):
    """触发条件类型"""
    PRICE_DIFFERENCE = "price_difference"  # 价差
    FUNDING_RATE_DIFF = "funding_rate_diff"  # 资金费率差异
    BASIS_RATE = "basis_rate"  # 基差率
    CUSTOM = "custom"  # 自定义条件


@dataclass
class HedgingCondition:
    """套保触发条件"""
    condition_id: str
    condition_type: TriggerType
    long_exchange: str
    long_symbol: str
    long_is_spot: bool
    short_exchange: str
    short_symbol: str
    short_is_spot: bool
    trigger_value: float
    comparison_operator: str  # >, <, >=, <=, ==
    amount: float
    amount_currency: str  # USDT, USDC等
    is_active: bool = True
    priority: int = 1
    created_at: Optional[datetime] = None


@dataclass
class CombinedHedgingCondition:
    """组合套保条件（所有子条件都必须满足）"""
    condition_id: str
    conditions: list  # 子条件列表
    long_exchange: str
    long_symbol: str
    long_is_spot: bool
    short_exchange: str
    short_symbol: str
    short_is_spot: bool
    amount: float
    amount_currency: str  # USDT, USDC等
    is_active: bool = True
    priority: int = 1
    created_at: Optional[datetime] = None


@dataclass
class MarketData:
    """市场数据"""
    exchange: str
    symbol: str
    price: float
    timestamp: datetime
    is_spot: bool
    funding_rate: Optional[float] = None


class HedgingStrategy:
    """跨交易所套保策略引擎 - 纯策略逻辑，不负责组件初始化"""

    def __init__(self,
                 coordinator: CrossExchangeCoordinator,
                 risk_manager: RiskManager,
                 position_manager: HedgingPositionManager):
        """初始化套保策略

        Args:
            coordinator: 跨交易所协调器
            risk_manager: 风险管理器
            position_manager: 持仓管理器
        """
        self.is_running = False
        self._stop_event = asyncio.Event()

        # 注入的组件
        self.coordinator = coordinator
        self.risk_manager = risk_manager
        self.position_manager = position_manager
        self.market_data_manager = MarketDataManager.get_instance()
        self.ding_talk_manager = DingTalkManager.get_instance()

        # 策略配置
        self.conditions: Dict[str, HedgingCondition] = {}
        self.combined_conditions: Dict[str, CombinedHedgingCondition] = {}  # 组合条件
        self.check_interval = 5  # 检查间隔(秒)
        self.max_positions = 10  # 最大持仓数量
        self.min_amount = 100  # 最小交易金额(USDT)
        self.max_amount = 10000  # 最大交易金额(USDT)

        # 市场数据缓存
        self.market_data_cache: Dict[str, MarketData] = {}



    async def start(self):
        """启动策略 - 只负责策略逻辑，不启动组件监控"""
        try:
            self.is_running = True
            logger.info("启动跨交易所套保策略引擎")

            # 启动主策略循环
            strategy_task = asyncio.create_task(self._run_strategy_loop())

            # 启动市场数据更新
            market_data_task = asyncio.create_task(self._update_market_data_loop())

            # 等待停止信号
            await self._stop_event.wait()

            # 取消所有任务
            strategy_task.cancel()
            market_data_task.cancel()

            await asyncio.gather(strategy_task, market_data_task, return_exceptions=True)

        except Exception as e:
            logger.error(f"套保策略运行异常: {str(e)}")
        finally:
            self.is_running = False

    async def stop(self):
        """停止策略"""
        logger.info("停止跨交易所套保策略")
        self.is_running = False
        self._stop_event.set()

    async def add_condition(self, condition: HedgingCondition):
        """添加套保条件

        Args:
            condition: 套保条件
        """
        try:
            # 验证条件
            if not await self._validate_condition(condition):
                logger.error(f"套保条件验证失败: {condition.condition_id}")
                return False

            # 添加条件
            self.conditions[condition.condition_id] = condition
            logger.info(f"添加套保条件: {condition.condition_id}")

            return True

        except Exception as e:
            logger.error(f"添加套保条件失败: {str(e)}")
            return False

    async def remove_condition(self, condition_id: str) -> bool:
        """移除套保条件

        Args:
            condition_id: 条件ID

        Returns:
            bool: 是否移除成功
        """
        try:
            if condition_id in self.conditions:
                del self.conditions[condition_id]
                logger.info(f"移除套保条件: {condition_id}")
                return True
            else:
                logger.warning(f"套保条件不存在: {condition_id}")
                return False

        except Exception as e:
            logger.error(f"移除套保条件失败: {str(e)}")
            return False

    async def add_combined_condition(self, combined_condition: CombinedHedgingCondition) -> bool:
        """添加组合套保条件

        Args:
            combined_condition: 组合套保条件

        Returns:
            bool: 是否添加成功
        """
        try:
            # 验证组合条件
            if not await self._validate_combined_condition(combined_condition):
                logger.error(f"组合套保条件验证失败: {combined_condition.condition_id}")
                return False

            # 添加组合条件
            self.combined_conditions[combined_condition.condition_id] = combined_condition
            logger.info(f"添加组合套保条件: {combined_condition.condition_id}, 包含 {len(combined_condition.conditions)} 个子条件")

            return True

        except Exception as e:
            logger.error(f"添加组合套保条件失败: {str(e)}")
            return False

    async def remove_combined_condition(self, condition_id: str) -> bool:
        """移除组合套保条件

        Args:
            condition_id: 组合条件ID

        Returns:
            bool: 是否移除成功
        """
        try:
            if condition_id in self.combined_conditions:
                del self.combined_conditions[condition_id]
                logger.info(f"移除组合套保条件: {condition_id}")
                return True
            else:
                logger.warning(f"组合套保条件不存在: {condition_id}")
                return False

        except Exception as e:
            logger.error(f"移除组合套保条件失败: {str(e)}")
            return False

    async def _run_strategy_loop(self):
        """主策略循环"""
        while self.is_running:
            try:
                # 检查所有活跃的单个条件
                for condition_id, condition in self.conditions.items():
                    if not condition.is_active:
                        continue

                    # 检查是否满足触发条件
                    if await self._check_trigger_condition(condition):
                        # 执行套保
                        await self._execute_hedging(condition)

                # 检查所有活跃的组合条件
                for condition_id, combined_condition in self.combined_conditions.items():
                    if not combined_condition.is_active:
                        continue

                    # 检查是否满足所有子条件
                    if await self._check_combined_trigger_condition(combined_condition):
                        # 执行套保
                        await self._execute_combined_hedging(combined_condition)

                # 等待下次检查
                await asyncio.sleep(self.check_interval)

            except Exception as e:
                logger.error(f"策略循环异常: {str(e)}")
                await asyncio.sleep(5)

    async def _update_market_data_loop(self):
        """更新市场数据循环"""
        while self.is_running:
            try:
                # 更新所有相关交易对的市场数据
                await self._update_market_data()

                # 等待下次更新
                await asyncio.sleep(2)  # 每2秒更新一次

            except Exception as e:
                logger.error(f"更新市场数据异常: {str(e)}")
                await asyncio.sleep(5)

    async def _check_trigger_condition(self, condition: HedgingCondition) -> bool:
        """检查是否满足触发条件

        Args:
            condition: 套保条件

        Returns:
            bool: 是否满足条件
        """
        try:
            if condition.condition_type == TriggerType.PRICE_DIFFERENCE:
                return await self._check_price_difference(condition)
            elif condition.condition_type == TriggerType.FUNDING_RATE_DIFF:
                return await self._check_funding_rate_diff(condition)
            elif condition.condition_type == TriggerType.BASIS_RATE:
                return await self._check_basis_rate(condition)
            elif condition.condition_type == TriggerType.CUSTOM:
                return await self._check_custom_condition(condition)
            else:
                logger.warning(f"未知的触发条件类型: {condition.condition_type}")
                return False

        except Exception as e:
            logger.error(f"检查触发条件失败: {str(e)}")
            return False

    async def _check_price_difference(self, condition: HedgingCondition) -> bool:
        """检查价差条件"""
        try:
            # 获取多头价格
            long_key = f"{condition.long_exchange}_{condition.long_symbol}_{condition.long_is_spot}"
            long_data = self.market_data_cache.get(long_key)

            # 获取空头价格
            short_key = f"{condition.short_exchange}_{condition.short_symbol}_{condition.short_is_spot}"
            short_data = self.market_data_cache.get(short_key)

            if not long_data or not short_data:
                return False

            # 计算价差
            price_diff = long_data.price - short_data.price

            # 检查触发条件
            return self._compare_value(price_diff, condition.trigger_value, condition.comparison_operator)

        except Exception as e:
            logger.error(f"检查价差条件失败: {str(e)}")
            return False

    async def _check_funding_rate_diff(self, condition: HedgingCondition) -> bool:
        """检查资金费率差异条件"""
        try:
            # 获取多头资金费率
            long_key = f"{condition.long_exchange}_{condition.long_symbol}_{condition.long_is_spot}"
            long_data = self.market_data_cache.get(long_key)

            # 获取空头资金费率
            short_key = f"{condition.short_exchange}_{condition.short_symbol}_{condition.short_is_spot}"
            short_data = self.market_data_cache.get(short_key)

            if not long_data or not short_data or long_data.funding_rate is None or short_data.funding_rate is None:
                return False

            # 计算资金费率差异
            funding_diff = long_data.funding_rate - short_data.funding_rate

            # 检查触发条件
            return self._compare_value(funding_diff, condition.trigger_value, condition.comparison_operator)

        except Exception as e:
            logger.error(f"检查资金费率差异条件失败: {str(e)}")
            return False

    async def _check_basis_rate(self, condition: HedgingCondition) -> bool:
        """检查基差率条件"""
        try:
            # 获取现货价格
            spot_key = f"{condition.long_exchange}_{condition.long_symbol}_True"
            spot_data = self.market_data_cache.get(spot_key)

            # 获取期货价格
            futures_key = f"{condition.short_exchange}_{condition.short_symbol}_False"
            futures_data = self.market_data_cache.get(futures_key)

            if not spot_data or not futures_data:
                return False

            # 计算基差率
            basis_rate = (futures_data.price - spot_data.price) / spot_data.price * 100

            # 检查触发条件
            return self._compare_value(basis_rate, condition.trigger_value, condition.comparison_operator)

        except Exception as e:
            logger.error(f"检查基差率条件失败: {str(e)}")
            return False

    async def _check_custom_condition(self, condition: HedgingCondition) -> bool:
        """检查自定义条件"""
        # TODO: 实现自定义条件逻辑
        logger.warning("自定义条件检查尚未实现")
        return False

    def _compare_value(self, actual_value: float, target_value: float, operator: str) -> bool:
        """比较数值

        Args:
            actual_value: 实际值
            target_value: 目标值
            operator: 比较运算符

        Returns:
            bool: 比较结果
        """
        if operator == ">":
            return actual_value > target_value
        elif operator == "<":
            return actual_value < target_value
        elif operator == ">=":
            return actual_value >= target_value
        elif operator == "<=":
            return actual_value <= target_value
        elif operator == "==":
            return abs(actual_value - target_value) < 1e-8
        else:
            logger.warning(f"未知的比较运算符: {operator}")
            return False

    async def _execute_hedging(self, condition: HedgingCondition):
        """执行套保操作

        Args:
            condition: 触发的套保条件
        """
        try:
            # 检查风险限制
            if not await self.risk_manager.check_risk_limits(condition):
                logger.warning(f"风险检查未通过，跳过套保执行: {condition.condition_id}")
                return

            # 检查持仓数量限制
            current_positions = len(self.position_manager.get_active_positions())
            if current_positions >= self.max_positions:
                logger.warning(f"已达到最大持仓数量限制: {current_positions}/{self.max_positions}")
                return

            # 验证交易金额（转换为USDT等价值进行验证）
            amount_usdt_equivalent = condition.amount
            if condition.amount_currency != 'USDT':
                # TODO: 实现汇率转换，这里简化处理
                if condition.amount_currency == 'USDC':
                    amount_usdt_equivalent = condition.amount  # 1:1假设
                else:
                    logger.warning(f"暂不支持的交易币种: {condition.amount_currency}")
                    return

            if amount_usdt_equivalent < self.min_amount or amount_usdt_equivalent > self.max_amount:
                logger.warning(f"交易金额超出限制: {amount_usdt_equivalent} (范围: {self.min_amount}-{self.max_amount})")
                return

            logger.info(f"开始执行套保: {condition.condition_id}")

            # 执行跨交易所套保
            hedging_pair = await self.coordinator.execute_hedging_pair(
                long_exchange=condition.long_exchange,
                long_symbol=condition.long_symbol,
                short_exchange=condition.short_exchange,
                short_symbol=condition.short_symbol,
                amount=condition.amount,
                amount_currency=condition.amount_currency,
                long_is_spot=condition.long_is_spot,
                short_is_spot=condition.short_is_spot
            )

            if hedging_pair:
                # 添加到持仓管理器
                await self.position_manager.add_position(hedging_pair)

                # 发送成功通知
                message = (
                    f"套保策略执行成功\n"
                    f"条件ID: {condition.condition_id}\n"
                    f"配对ID: {hedging_pair.pair_id}\n"
                    f"多头: {condition.long_exchange} {condition.long_symbol}\n"
                    f"空头: {condition.short_exchange} {condition.short_symbol}\n"
                    f"金额: {condition.amount:.2f} {condition.amount_currency}"
                )
                self.ding_talk_manager.send_message(message, prefix="套保成功")

                # 暂时禁用该条件，避免重复触发
                condition.is_active = False
                logger.info(f"套保条件已暂时禁用: {condition.condition_id}")

            else:
                logger.error(f"套保执行失败: {condition.condition_id}")

        except Exception as e:
            logger.error(f"执行套保失败: {str(e)}")

    async def _update_market_data(self):
        """更新市场数据"""
        try:
            # 收集所有需要的交易对
            symbols_to_update = set()

            # 收集单个条件的交易对
            for condition in self.conditions.values():
                if condition.is_active:
                    symbols_to_update.add((condition.long_exchange, condition.long_symbol, condition.long_is_spot))
                    symbols_to_update.add((condition.short_exchange, condition.short_symbol, condition.short_is_spot))

            # 收集组合条件的交易对
            for combined_condition in self.combined_conditions.values():
                if combined_condition.is_active:
                    symbols_to_update.add((combined_condition.long_exchange, combined_condition.long_symbol, combined_condition.long_is_spot))
                    symbols_to_update.add((combined_condition.short_exchange, combined_condition.short_symbol, combined_condition.short_is_spot))

            # 更新市场数据
            for exchange, symbol, is_spot in symbols_to_update:
                try:
                    # 获取价格
                    price = await self.coordinator._get_current_price(exchange, symbol, is_spot)

                    if price:
                        # 获取资金费率（仅期货）
                        funding_rate = None
                        if not is_spot:
                            funding_rate = await self._get_funding_rate(exchange, symbol)

                        # 更新缓存
                        key = f"{exchange}_{symbol}_{is_spot}"
                        self.market_data_cache[key] = MarketData(
                            exchange=exchange,
                            symbol=symbol,
                            price=price,
                            timestamp=datetime.now(timezone.utc),
                            is_spot=is_spot,
                            funding_rate=funding_rate
                        )

                except Exception as e:
                    logger.error(f"更新市场数据失败: {exchange} {symbol} - {str(e)}")

        except Exception as e:
            logger.error(f"更新市场数据异常: {str(e)}")

    async def _get_funding_rate(self, exchange: str, symbol: str) -> Optional[float]:
        """获取资金费率

        Args:
            exchange: 交易所名称
            symbol: 交易对

        Returns:
            float: 资金费率，失败返回None
        """
        try:
            client = self.coordinator.exchange_clients.get(exchange)
            if not client:
                return None

            funding_rate = await client.fetch_funding_rate(symbol)
            return funding_rate.get('fundingRate') if funding_rate else None

        except Exception as e:
            logger.error(f"获取资金费率失败: {exchange} {symbol} - {str(e)}")
            return None

    async def _validate_condition(self, condition: HedgingCondition) -> bool:
        """验证套保条件

        Args:
            condition: 套保条件

        Returns:
            bool: 是否有效
        """
        try:
            # 检查交易所是否支持
            if condition.long_exchange not in self.exchange_names:
                logger.error(f"不支持的多头交易所: {condition.long_exchange}")
                return False

            if condition.short_exchange not in self.exchange_names:
                logger.error(f"不支持的空头交易所: {condition.short_exchange}")
                return False

            # 检查交易金额
            if condition.amount <= 0:
                logger.error(f"无效的交易金额: {condition.amount}")
                return False

            # 检查交易币种
            if not condition.amount_currency:
                logger.error("交易币种不能为空")
                return False

            # 检查比较运算符
            if condition.comparison_operator not in ['>', '<', '>=', '<=', '==']:
                logger.error(f"无效的比较运算符: {condition.comparison_operator}")
                return False

            # 检查交易对是否存在
            # TODO: 添加交易对存在性检查

            return True

        except Exception as e:
            logger.error(f"验证套保条件失败: {str(e)}")
            return False

    def get_conditions(self) -> Dict[str, HedgingCondition]:
        """获取所有套保条件"""
        return self.conditions.copy()

    def get_market_data(self) -> Dict[str, MarketData]:
        """获取市场数据缓存"""
        return self.market_data_cache.copy()

    def get_status(self) -> Dict:
        """获取策略状态"""
        return {
            'is_running': self.is_running,
            'conditions_count': len(self.conditions),
            'active_conditions_count': len([c for c in self.conditions.values() if c.is_active]),
            'market_data_count': len(self.market_data_cache),
            'coordinator_status': self.coordinator.get_exchange_status(),
            'active_positions': len(self.position_manager.get_active_positions()),
            'max_positions': self.max_positions
        }

    async def cleanup(self):
        """清理策略资源 - 不负责清理注入的组件"""
        try:
            # 停止策略
            await self.stop()

            # 清空缓存
            self.conditions.clear()
            self.market_data_cache.clear()

            logger.info("套保策略引擎清理完成")

        except Exception as e:
            logger.error(f"清理套保策略失败: {str(e)}")

    async def _check_combined_trigger_condition(self, combined_condition: CombinedHedgingCondition) -> bool:
        """检查组合触发条件（所有子条件都必须满足）

        Args:
            combined_condition: 组合套保条件

        Returns:
            bool: 是否所有子条件都满足
        """
        try:
            logger.debug(f"检查组合条件: {combined_condition.condition_id}")

            # 检查所有子条件
            for sub_condition_data in combined_condition.conditions:
                # 创建临时的HedgingCondition对象用于检查
                temp_condition = HedgingCondition(
                    condition_id=f"temp_{sub_condition_data['type']}",
                    condition_type=TriggerType(sub_condition_data['type']),
                    long_exchange=combined_condition.long_exchange,
                    long_symbol=combined_condition.long_symbol,
                    long_is_spot=combined_condition.long_is_spot,
                    short_exchange=combined_condition.short_exchange,
                    short_symbol=combined_condition.short_symbol,
                    short_is_spot=combined_condition.short_is_spot,
                    trigger_value=sub_condition_data['trigger_value'],
                    comparison_operator=sub_condition_data.get('comparison_operator', '>'),
                    amount=combined_condition.amount,
                    amount_currency=combined_condition.amount_currency
                )

                # 检查这个子条件是否满足
                if not await self._check_trigger_condition(temp_condition):
                    logger.debug(f"子条件未满足: {sub_condition_data['type']}")
                    return False

            logger.info(f"所有子条件都满足，组合条件触发: {combined_condition.condition_id}")
            return True

        except Exception as e:
            logger.error(f"检查组合触发条件失败: {str(e)}")
            return False

    async def _execute_combined_hedging(self, combined_condition: CombinedHedgingCondition):
        """执行组合套保操作（新架构：使用拆单和任务队列）

        Args:
            combined_condition: 触发的组合套保条件
        """
        try:
            logger.info(f"开始执行组合套保: {combined_condition.condition_id}")

            # 检查风险限制
            if not await self.risk_manager.check_combined_risk_limits(combined_condition):
                logger.warning(f"风险检查未通过，跳过组合套保执行: {combined_condition.condition_id}")
                return

            # 检查持仓数量限制
            current_positions = len(self.position_manager.get_active_positions())
            if current_positions >= self.max_positions:
                logger.warning(f"已达到最大持仓数量限制: {current_positions}/{self.max_positions}")
                return

            # 验证交易金额
            amount_usdt_equivalent = combined_condition.amount
            if combined_condition.amount_currency != 'USDT':
                if combined_condition.amount_currency == 'USDC':
                    amount_usdt_equivalent = combined_condition.amount  # 1:1假设
                else:
                    logger.warning(f"暂不支持的交易币种: {combined_condition.amount_currency}")
                    return

            if amount_usdt_equivalent < self.min_amount or amount_usdt_equivalent > self.max_amount:
                logger.warning(f"交易金额超出限制: {amount_usdt_equivalent} (范围: {self.min_amount}-{self.max_amount})")
                return

            # 创建临时订单对象用于拆单
            from .order_cache_manager import HedgingOrder, ExecutionType, OrderStatus, get_order_cache_manager
            from .order_splitter import get_order_splitter
            from .task_executor import get_task_executor

            # 生成临时订单ID
            temp_order_id = f"{combined_condition.condition_id}_triggered"

            temp_order = HedgingOrder(
                order_id=temp_order_id,
                execution_type=ExecutionType.IMMEDIATE,
                conditions=[],  # 已触发，无需条件
                long_exchange=combined_condition.long_exchange,
                long_symbol=combined_condition.long_symbol,
                long_is_spot=combined_condition.long_is_spot,
                short_exchange=combined_condition.short_exchange,
                short_symbol=combined_condition.short_symbol,
                short_is_spot=combined_condition.short_is_spot,
                amount=combined_condition.amount,
                amount_currency=combined_condition.amount_currency,
                priority=combined_condition.priority,
                status=OrderStatus.CREATED
            )

            # 保存临时订单
            order_cache = get_order_cache_manager()
            await order_cache.save_order(temp_order)

            # 进行智能拆单
            order_splitter = get_order_splitter()
            split_tasks = await order_splitter.split_order(temp_order)

            if not split_tasks:
                logger.error(f"组合条件触发后拆单失败: {combined_condition.condition_id}")
                return

            # 保存拆单任务
            await order_cache.save_split_tasks(temp_order_id, split_tasks)

            # 启动任务执行器（如果还未启动）
            task_executor = get_task_executor()
            if not task_executor.is_running:
                await task_executor.start()

            # 发送成功通知
            condition_types = [c['type'] for c in combined_condition.conditions]
            message = (
                f"组合套保条件触发成功\n"
                f"条件ID: {combined_condition.condition_id}\n"
                f"临时订单ID: {temp_order_id}\n"
                f"触发条件: {', '.join(condition_types)}\n"
                f"多头: {combined_condition.long_exchange} {combined_condition.long_symbol}\n"
                f"空头: {combined_condition.short_exchange} {combined_condition.short_symbol}\n"
                f"金额: {combined_condition.amount:.2f} {combined_condition.amount_currency}\n"
                f"拆单数量: {len(split_tasks)} 个任务"
            )
            self.ding_talk_manager.send_message(message, prefix="组合套保触发")

            # 暂时禁用该组合条件，避免重复触发
            combined_condition.is_active = False
            logger.info(f"组合套保条件已暂时禁用: {combined_condition.condition_id}")
            logger.info(f"组合套保已提交到任务队列: {temp_order_id}, 拆分为 {len(split_tasks)} 个任务")

        except Exception as e:
            logger.error(f"执行组合套保失败: {str(e)}")

    async def _validate_combined_condition(self, combined_condition: CombinedHedgingCondition) -> bool:
        """验证组合套保条件

        Args:
            combined_condition: 组合套保条件

        Returns:
            bool: 是否有效
        """
        try:
            # 检查子条件数量
            if not combined_condition.conditions or len(combined_condition.conditions) == 0:
                logger.error("组合条件必须包含至少一个子条件")
                return False

            # 检查交易所是否支持
            if combined_condition.long_exchange not in self.coordinator.exchange_clients:
                logger.error(f"不支持的多头交易所: {combined_condition.long_exchange}")
                return False

            if combined_condition.short_exchange not in self.coordinator.exchange_clients:
                logger.error(f"不支持的空头交易所: {combined_condition.short_exchange}")
                return False

            # 检查交易金额
            if combined_condition.amount <= 0:
                logger.error(f"无效的交易金额: {combined_condition.amount}")
                return False

            # 检查交易币种
            if not combined_condition.amount_currency:
                logger.error("交易币种不能为空")
                return False

            # 验证每个子条件
            for sub_condition in combined_condition.conditions:
                if 'type' not in sub_condition:
                    logger.error("子条件缺少type字段")
                    return False

                if 'trigger_value' not in sub_condition:
                    logger.error("子条件缺少trigger_value字段")
                    return False

                try:
                    TriggerType(sub_condition['type'])
                except ValueError:
                    logger.error(f"无效的子条件类型: {sub_condition['type']}")
                    return False

                comparison_operator = sub_condition.get('comparison_operator', '>')
                if comparison_operator not in ['>', '<', '>=', '<=', '==']:
                    logger.error(f"无效的比较运算符: {comparison_operator}")
                    return False

            return True

        except Exception as e:
            logger.error(f"验证组合套保条件失败: {str(e)}")
            return False

    async def create_hedging_order(self,
                                  order_id: str,
                                  conditions: list,
                                  long_exchange: str,
                                  long_symbol: str,
                                  short_exchange: str,
                                  short_symbol: str,
                                  amount: float,
                                  amount_currency: str,
                                  long_is_spot: bool = True,
                                  short_is_spot: bool = False,
                                  priority: int = 1) -> dict:
        """创建套保订单（策略引擎统一处理）

        新架构：订单创建后立即返回，后台异步处理
        - 创建订单并保存到缓存
        - 进行智能拆单
        - 提交到任务队列异步执行

        Args:
            order_id: 订单ID
            conditions: 触发条件列表
            long_exchange: 多头交易所
            long_symbol: 多头交易对
            short_exchange: 空头交易所
            short_symbol: 空头交易对
            amount: 交易金额
            amount_currency: 交易金额币种
            long_is_spot: 多头是否为现货
            short_is_spot: 空头是否为现货
            priority: 优先级

        Returns:
            dict: 创建结果（立即返回）
        """
        try:
            logger.info(f"策略引擎处理套保订单: {order_id}, 条件数量: {len(conditions)}")

            # 导入必要的模块
            from .order_cache_manager import HedgingOrder, ExecutionType, OrderStatus, get_order_cache_manager
            from .order_splitter import get_order_splitter
            from .task_executor import get_task_executor

            # 确定执行类型
            execution_type = ExecutionType.IMMEDIATE if len(conditions) == 0 else ExecutionType.CONDITIONAL

            # 创建订单对象
            order = HedgingOrder(
                order_id=order_id,
                execution_type=execution_type,
                conditions=conditions,
                long_exchange=long_exchange,
                long_symbol=long_symbol,
                long_is_spot=long_is_spot,
                short_exchange=short_exchange,
                short_symbol=short_symbol,
                short_is_spot=short_is_spot,
                amount=amount,
                amount_currency=amount_currency,
                priority=priority,
                status=OrderStatus.CREATED
            )

            # 保存订单到缓存
            order_cache = get_order_cache_manager()
            save_success = await order_cache.save_order(order)

            if not save_success:
                return {
                    'success': False,
                    'error': '保存订单到缓存失败'
                }

            # 启动后台处理任务
            asyncio.create_task(self._process_order_async(order))

            # 立即返回创建成功结果
            return {
                'success': True,
                'order_id': order_id,
                'execution_type': execution_type.value,
                'status': 'created',
                'message': '订单创建成功，正在后台处理',
                'conditions': [{
                    'order_id': order_id,
                    'execution_type': execution_type.value,
                    'status': 'created',
                    'amount': amount,
                    'amount_currency': amount_currency,
                    'created_at': order.created_at.isoformat() if order.created_at else None,
                    'processing_status': '订单已创建，正在进行拆单处理'
                }]
            }

        except Exception as e:
            logger.error(f"策略引擎处理套保订单失败: {str(e)}")
            return {'success': False, 'error': f'策略引擎处理失败: {str(e)}'}

    async def _execute_immediate_hedging_internal(self,
                                                 order_id: str,
                                                 long_exchange: str,
                                                 long_symbol: str,
                                                 short_exchange: str,
                                                 short_symbol: str,
                                                 amount: float,
                                                 amount_currency: str,
                                                 long_is_spot: bool = True,
                                                 short_is_spot: bool = False) -> dict:
        """策略引擎内部立即执行套保

        Args:
            order_id: 订单ID
            long_exchange: 多头交易所
            long_symbol: 多头交易对
            short_exchange: 空头交易所
            short_symbol: 空头交易对
            amount: 交易金额
            amount_currency: 交易金额币种
            long_is_spot: 多头是否为现货
            short_is_spot: 空头是否为现货

        Returns:
            dict: 执行结果
        """
        try:
            logger.info(f"策略引擎立即执行套保: {order_id}")

            # 验证交易金额
            amount_usdt_equivalent = amount
            if amount_currency != 'USDT':
                if amount_currency == 'USDC':
                    amount_usdt_equivalent = amount  # 1:1假设
                else:
                    logger.warning(f"暂不支持的交易币种: {amount_currency}")
                    return {
                        'execution_id': f"{order_id}_immediate",
                        'execution_type': 'immediate',
                        'status': 'failed',
                        'error': f'暂不支持的交易币种: {amount_currency}',
                        'executed_at': datetime.now(timezone.utc).isoformat()
                    }

            # 验证金额范围
            if amount_usdt_equivalent < self.min_amount or amount_usdt_equivalent > self.max_amount:
                error_msg = f"交易金额超出限制: {amount_usdt_equivalent} (范围: {self.min_amount}-{self.max_amount})"
                logger.warning(error_msg)
                return {
                    'execution_id': f"{order_id}_immediate",
                    'execution_type': 'immediate',
                    'status': 'failed',
                    'error': error_msg,
                    'executed_at': datetime.now(timezone.utc).isoformat()
                }

            # 检查持仓数量限制
            current_positions = len(self.position_manager.get_active_positions())
            if current_positions >= self.max_positions:
                error_msg = f"已达到最大持仓数量限制: {current_positions}/{self.max_positions}"
                logger.warning(error_msg)
                return {
                    'execution_id': f"{order_id}_immediate",
                    'execution_type': 'immediate',
                    'status': 'failed',
                    'error': error_msg,
                    'executed_at': datetime.now(timezone.utc).isoformat()
                }

            # 执行跨交易所套保
            hedging_pair = await self.coordinator.execute_hedging_pair(
                long_exchange=long_exchange,
                long_symbol=long_symbol,
                short_exchange=short_exchange,
                short_symbol=short_symbol,
                amount=amount,
                amount_currency=amount_currency,
                long_is_spot=long_is_spot,
                short_is_spot=short_is_spot
            )

            if hedging_pair:
                # 添加到持仓管理器
                await self.position_manager.add_position(hedging_pair)

                # 发送成功通知
                message = (
                    f"立即套保执行成功\n"
                    f"订单ID: {order_id}\n"
                    f"配对ID: {hedging_pair.pair_id}\n"
                    f"多头: {long_exchange} {long_symbol}\n"
                    f"空头: {short_exchange} {short_symbol}\n"
                    f"金额: {amount:.2f} {amount_currency}"
                )
                self.ding_talk_manager.send_message(message, prefix="立即套保")

                return {
                    'execution_id': f"{order_id}_immediate",
                    'execution_type': 'immediate',
                    'long_exchange': long_exchange,
                    'long_symbol': long_symbol,
                    'long_is_spot': long_is_spot,
                    'short_exchange': short_exchange,
                    'short_symbol': short_symbol,
                    'short_is_spot': short_is_spot,
                    'amount': amount,
                    'amount_currency': amount_currency,
                    'status': 'executed',
                    'executed_at': datetime.now(timezone.utc).isoformat(),
                    'hedging_pair': {
                        'pair_id': hedging_pair.pair_id,
                        'long_order': hedging_pair.long_order,
                        'short_order': hedging_pair.short_order
                    }
                }
            else:
                return {
                    'execution_id': f"{order_id}_immediate",
                    'execution_type': 'immediate',
                    'status': 'failed',
                    'error': '套保执行失败',
                    'executed_at': datetime.now(timezone.utc).isoformat()
                }

        except Exception as e:
            logger.error(f"策略引擎立即执行套保失败: {str(e)}")
            return {
                'execution_id': f"{order_id}_immediate",
                'execution_type': 'immediate',
                'status': 'failed',
                'error': f'执行失败: {str(e)}',
                'executed_at': datetime.now(timezone.utc).isoformat()
            }

    async def _process_order_async(self, order):
        """异步处理订单（后台任务）

        Args:
            order: 套保订单对象
        """
        try:
            logger.info(f"开始后台处理订单: {order.order_id}")

            # 导入必要的模块
            from .order_cache_manager import OrderStatus, get_order_cache_manager
            from .order_splitter import get_order_splitter
            from .task_executor import get_task_executor

            order_cache = get_order_cache_manager()
            order_splitter = get_order_splitter()
            task_executor = get_task_executor()

            # 策略引擎内部判断：无条件则立即处理，有条件则监控等待
            if len(order.conditions) == 0:
                # 无条件，立即进行拆单和执行
                logger.info(f"策略引擎判断：无触发条件，立即处理订单: {order.order_id}")

                # 更新订单状态为拆单中
                await order_cache.update_order_status(order.order_id, OrderStatus.SPLITTING)

                # 进行智能拆单
                split_tasks = await order_splitter.split_order(order)

                if not split_tasks:
                    logger.error(f"拆单失败: {order.order_id}")
                    await order_cache.update_order_status(order.order_id, OrderStatus.FAILED)
                    return

                # 保存拆单任务
                await order_cache.save_split_tasks(order.order_id, split_tasks)

                # 启动任务执行器（如果还未启动）
                if not task_executor.is_running:
                    await task_executor.start()

                logger.info(f"立即执行订单处理完成: {order.order_id}, 已拆分为 {len(split_tasks)} 个任务")

            else:
                # 有条件，添加到监控队列
                logger.info(f"策略引擎判断：有 {len(order.conditions)} 个触发条件，添加到监控队列: {order.order_id}")

                # 创建组合条件并添加到监控
                monitoring_result = await self._add_to_monitoring_queue_new(order)

                if not monitoring_result['success']:
                    logger.error(f"添加到监控队列失败: {order.order_id}")
                    await order_cache.update_order_status(order.order_id, OrderStatus.FAILED)
                    return

                logger.info(f"条件监控订单处理完成: {order.order_id}")

        except Exception as e:
            logger.error(f"后台处理订单失败: {order.order_id}, 错误: {str(e)}")
            # 更新订单状态为失败
            try:
                from .order_cache_manager import OrderStatus, get_order_cache_manager
                order_cache = get_order_cache_manager()
                await order_cache.update_order_status(order.order_id, OrderStatus.FAILED)
            except:
                pass

    async def _add_to_monitoring_queue_new(self, order) -> dict:
        """添加到监控队列（新版本）

        Args:
            order: 套保订单对象

        Returns:
            dict: 添加结果
        """
        try:
            logger.info(f"添加订单到监控队列: {order.order_id}")

            # 创建组合条件ID
            combined_condition_id = f"{order.order_id}_combined"

            # 创建组合套保条件
            combined_condition = CombinedHedgingCondition(
                condition_id=combined_condition_id,
                conditions=order.conditions,
                long_exchange=order.long_exchange,
                long_symbol=order.long_symbol,
                long_is_spot=order.long_is_spot,
                short_exchange=order.short_exchange,
                short_symbol=order.short_symbol,
                short_is_spot=order.short_is_spot,
                amount=order.amount,
                amount_currency=order.amount_currency,
                priority=order.priority,
                created_at=datetime.now(timezone.utc)
            )

            # 验证组合条件
            if not await self._validate_combined_condition(combined_condition):
                logger.error(f"组合套保条件验证失败: {combined_condition_id}")
                return {
                    'success': False,
                    'error': f'组合套保条件验证失败: {combined_condition_id}'
                }

            # 添加到组合条件监控队列
            self.combined_conditions[combined_condition_id] = combined_condition
            logger.info(f"成功添加组合套保条件到监控队列: {combined_condition_id}, 包含 {len(order.conditions)} 个子条件")

            # 发送通知
            condition_types = [c['type'] for c in order.conditions]
            message = (
                f"新增组合套保条件监控\n"
                f"订单ID: {order.order_id}\n"
                f"条件ID: {combined_condition_id}\n"
                f"子条件: {', '.join(condition_types)}\n"
                f"多头: {order.long_exchange} {order.long_symbol}\n"
                f"空头: {order.short_exchange} {order.short_symbol}\n"
                f"金额: {order.amount} {order.amount_currency}\n"
                f"逻辑: 所有条件都必须满足"
            )
            self.ding_talk_manager.send_message(message, prefix="组合套保监控")

            return {
                'success': True,
                'combined_condition_id': combined_condition_id
            }

        except Exception as e:
            logger.error(f"添加到监控队列失败: {str(e)}")
            return {
                'success': False,
                'error': f'添加到监控队列失败: {str(e)}'
            }

    async def _add_to_monitoring_queue(self,
                                      order_id: str,
                                      conditions: list,
                                      long_exchange: str,
                                      long_symbol: str,
                                      short_exchange: str,
                                      short_symbol: str,
                                      amount: float,
                                      amount_currency: str,
                                      long_is_spot: bool = True,
                                      short_is_spot: bool = False,
                                      priority: int = 1) -> dict:
        """添加到监控队列（策略引擎内部方法）

        Args:
            order_id: 订单ID
            conditions: 触发条件列表
            long_exchange: 多头交易所
            long_symbol: 多头交易对
            short_exchange: 空头交易所
            short_symbol: 空头交易对
            amount: 交易金额
            amount_currency: 交易金额币种
            long_is_spot: 多头是否为现货
            short_is_spot: 空头是否为现货
            priority: 优先级

        Returns:
            dict: 添加结果
        """
        try:
            logger.info(f"策略引擎添加到监控队列: {order_id}")

            # 创建组合条件ID
            combined_condition_id = f"{order_id}_combined"

            # 创建组合套保条件
            combined_condition = CombinedHedgingCondition(
                condition_id=combined_condition_id,
                conditions=conditions,
                long_exchange=long_exchange,
                long_symbol=long_symbol,
                long_is_spot=long_is_spot,
                short_exchange=short_exchange,
                short_symbol=short_symbol,
                short_is_spot=short_is_spot,
                amount=amount,
                amount_currency=amount_currency,
                priority=priority,
                created_at=datetime.now(timezone.utc)
            )

            # 验证组合条件
            if not await self._validate_combined_condition(combined_condition):
                logger.error(f"组合套保条件验证失败: {combined_condition_id}")
                return {
                    'success': False,
                    'error': f'组合套保条件验证失败: {combined_condition_id}'
                }

            # 添加到组合条件监控队列
            self.combined_conditions[combined_condition_id] = combined_condition
            logger.info(f"成功添加组合套保条件到监控队列: {combined_condition_id}, 包含 {len(conditions)} 个子条件")

            # 发送通知
            condition_types = [c['type'] for c in conditions]
            message = (
                f"新增组合套保条件监控\n"
                f"订单ID: {order_id}\n"
                f"条件ID: {combined_condition_id}\n"
                f"子条件: {', '.join(condition_types)}\n"
                f"多头: {long_exchange} {long_symbol}\n"
                f"空头: {short_exchange} {short_symbol}\n"
                f"金额: {amount} {amount_currency}\n"
                f"逻辑: 所有条件都必须满足"
            )
            self.ding_talk_manager.send_message(message, prefix="组合套保监控")

            # 构建条件数据
            condition_data = {
                'condition_id': combined_condition_id,
                'condition_type': 'combined',
                'conditions': conditions,  # 保存所有子条件
                'long_exchange': long_exchange,
                'long_symbol': long_symbol,
                'long_is_spot': long_is_spot,
                'short_exchange': short_exchange,
                'short_symbol': short_symbol,
                'short_is_spot': short_is_spot,
                'amount': amount,
                'amount_currency': amount_currency,
                'priority': priority,
                'created_at': datetime.now(timezone.utc).isoformat(),
                'status': 'monitoring',
                'logic': 'ALL_CONDITIONS_MUST_BE_MET'
            }

            return {
                'success': True,
                'condition_data': condition_data
            }

        except Exception as e:
            logger.error(f"策略引擎添加到监控队列失败: {str(e)}")
            return {
                'success': False,
                'error': f'添加到监控队列失败: {str(e)}'
            }
