"""
跨交易所套保策略使用示例

这个文件展示了如何正确使用套保策略框架
"""
import asyncio
import logging

from crypt_carry.strategies.hedging.hedging_manager import HedgingStrategyManager

logger = logging.getLogger(__name__)


async def main():
    """主函数 - 展示套保策略的完整使用流程"""

    # 1. 创建策略管理器（这是程序入口）
    # 可以指定交易所，也可以从配置文件读取
    manager = HedgingStrategyManager()  # 从配置文件读取交易所
    # 或者: manager = HedgingStrategyManager(["binance", "okx"])  # 手动指定

    try:
        # 2. 初始化策略管理器
        await manager.initialize()

        # 4. 添加套保条件
        # 示例1：价差套保条件
        await manager.add_hedging_condition(
            condition_id="btc_price_diff_1",
            condition_type="price_difference",
            long_exchange="binance",
            long_symbol="BTC/USDT",
            short_exchange="okx",
            short_symbol="BTC/USDT",
            trigger_value=50.0,  # 价差超过50 USDT时触发
            comparison_operator=">",
            amount_usdt=1000.0,
            long_is_spot=True,
            short_is_spot=True,
            priority=1
        )

        # 示例2：资金费率差异套保条件
        await manager.add_hedging_condition(
            condition_id="eth_funding_diff_1",
            condition_type="funding_rate_diff",
            long_exchange="binance",
            long_symbol="ETH/USDT",
            short_exchange="okx",
            short_symbol="ETH/USDT",
            trigger_value=0.001,  # 资金费率差异超过0.1%时触发
            comparison_operator=">",
            amount_usdt=2000.0,
            long_is_spot=False,  # 期货
            short_is_spot=False, # 期货
            priority=2
        )

        # 示例3：基差率套保条件
        await manager.add_hedging_condition(
            condition_id="btc_basis_rate_1",
            condition_type="basis_rate",
            long_exchange="binance",
            long_symbol="BTC/USDT",
            short_exchange="binance",
            short_symbol="BTC/USDT",
            trigger_value=0.5,  # 基差率超过0.5%时触发
            comparison_operator=">",
            amount_usdt=1500.0,
            long_is_spot=True,   # 现货
            short_is_spot=False, # 期货
            priority=3
        )

        # 5. 启动策略管理器
        logger.info("启动套保策略管理器...")

        # 创建启动任务
        manager_task = asyncio.create_task(manager.start())

        # 6. 监控策略状态（模拟运行一段时间）
        await asyncio.sleep(5)  # 等待5秒让策略启动

        # 获取状态信息
        status = manager.get_status()
        logger.info(f"策略状态: {status}")

        conditions = manager.get_conditions()
        logger.info(f"套保条件: {len(conditions)} 个")

        positions = manager.get_positions()
        logger.info(f"持仓摘要: {positions}")

        # 7. 模拟运行30秒后停止
        logger.info("策略运行中，30秒后停止...")
        await asyncio.sleep(30)

        # 8. 手动平仓示例（如果有持仓的话）
        # await manager.close_position("some_pair_id", "手动平仓测试")

        # 9. 移除套保条件示例
        await manager.remove_hedging_condition("btc_price_diff_1")

        # 10. 停止策略
        await manager.stop()

        # 等待管理器任务完成
        manager_task.cancel()
        try:
            await manager_task
        except asyncio.CancelledError:
            pass

    except Exception as e:
        logger.error(f"运行套保策略失败: {str(e)}")

    finally:
        # 11. 清理资源
        await manager.cleanup()
        logger.info("套保策略示例运行完成")


async def simple_example():
    """简单示例 - 最小化使用"""

    # 创建并初始化管理器
    manager = HedgingStrategyManager(["binance", "okx"])
    await manager.initialize()

    # 添加一个简单的价差套保条件
    await manager.add_hedging_condition(
        condition_id="simple_btc_hedge",
        condition_type="price_difference",
        long_exchange="binance",
        long_symbol="BTC/USDT",
        short_exchange="okx",
        short_symbol="BTC/USDT",
        trigger_value=100.0,
        comparison_operator=">",
        amount_usdt=1000.0
    )

    # 启动策略（这里只是示例，实际使用时需要适当的生命周期管理）
    logger.info("简单套保策略已配置完成")

    # 清理
    await manager.cleanup()


if __name__ == "__main__":
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    # 运行主示例
    asyncio.run(main())

    # 或者运行简单示例
    # asyncio.run(simple_example())
