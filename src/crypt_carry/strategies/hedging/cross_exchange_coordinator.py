"""
跨交易所协调器 - 管理多个交易所的连接和状态，协调跨交易所的订单执行
"""
import asyncio
import logging
from datetime import datetime, timezone
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum

from crypt_carry.core.exchange.base.exchange_client import ExchangeClient
from crypt_carry.core.exchange.factory.exchange_factory import ExchangeFactory
from crypt_carry.utils.ding_talk_manager import DingTalkManager

logger = logging.getLogger(__name__)


class OrderSide(Enum):
    """订单方向"""
    BUY = "buy"
    SELL = "sell"


class OrderType(Enum):
    """订单类型"""
    MARKET = "market"
    LIMIT = "limit"


@dataclass
class CrossExchangeOrder:
    """跨交易所订单"""
    exchange: str
    symbol: str
    side: OrderSide
    order_type: OrderType
    amount: float
    price: Optional[float] = None
    is_spot: bool = True  # True为现货，False为合约
    order_id: Optional[str] = None
    status: str = "pending"  # pending, filled, failed, cancelled
    filled_amount: float = 0.0
    filled_price: Optional[float] = None
    timestamp: Optional[datetime] = None
    error_message: Optional[str] = None


@dataclass
class HedgingPair:
    """套保交易对"""
    long_order: CrossExchangeOrder  # 多头订单
    short_order: CrossExchangeOrder  # 空头订单
    pair_id: str
    created_at: datetime
    status: str = "pending"  # pending, completed, failed, partial
    target_amount: float = 0.0  # 目标金额(USDT)


class CrossExchangeCoordinator:
    """跨交易所协调器"""

    def __init__(self):
        """初始化跨交易所协调器"""
        self.exchange_clients: Dict[str, ExchangeClient] = {}
        self.exchange_status: Dict[str, Dict] = {}
        self.active_orders: Dict[str, CrossExchangeOrder] = {}
        self.hedging_pairs: Dict[str, HedgingPair] = {}

        # 网络延迟监控
        self.latency_monitor: Dict[str, List[float]] = {}

        # 钉钉消息管理器
        self.ding_talk_manager = DingTalkManager.get_instance()

        # 协调器状态
        self.is_running = False
        self._stop_event = asyncio.Event()

    async def initialize(self, exchange_names: List[str]):
        """初始化交易所客户端

        Args:
            exchange_names: 交易所名称列表
        """
        try:
            logger.info(f"初始化跨交易所协调器，交易所: {exchange_names}")

            factory = ExchangeFactory()

            for exchange_name in exchange_names:
                try:
                    # 创建交易所客户端
                    client = await factory.create_exchange_client(exchange_name)
                    if client:
                        self.exchange_clients[exchange_name] = client
                        self.exchange_status[exchange_name] = {
                            'connected': True,
                            'last_ping': datetime.now(timezone.utc),
                            'error_count': 0,
                            'last_error': None
                        }
                        self.latency_monitor[exchange_name] = []
                        logger.info(f"成功连接到 {exchange_name}")
                    else:
                        logger.error(f"连接到 {exchange_name} 失败")

                except Exception as e:
                    logger.error(f"初始化 {exchange_name} 失败: {str(e)}")
                    self.exchange_status[exchange_name] = {
                        'connected': False,
                        'last_ping': None,
                        'error_count': 1,
                        'last_error': str(e)
                    }

            logger.info(f"跨交易所协调器初始化完成，成功连接 {len(self.exchange_clients)} 个交易所")

        except Exception as e:
            logger.error(f"跨交易所协调器初始化失败: {str(e)}")
            raise

    async def start_monitoring(self):
        """启动监控任务"""
        try:
            self.is_running = True
            logger.info("启动跨交易所协调器监控")

            # 启动各种监控任务
            tasks = [
                asyncio.create_task(self._monitor_exchange_status()),
                asyncio.create_task(self._monitor_network_latency()),
                asyncio.create_task(self._monitor_active_orders())
            ]

            # 等待停止信号
            await self._stop_event.wait()

            # 取消所有任务
            for task in tasks:
                task.cancel()

            await asyncio.gather(*tasks, return_exceptions=True)

        except Exception as e:
            logger.error(f"监控任务异常: {str(e)}")
        finally:
            self.is_running = False

    async def stop_monitoring(self):
        """停止监控"""
        logger.info("停止跨交易所协调器监控")
        self.is_running = False
        self._stop_event.set()

    async def execute_hedging_pair(self,
                                   long_exchange: str,
                                   long_symbol: str,
                                   short_exchange: str,
                                   short_symbol: str,
                                   amount_usdt: float,
                                   long_is_spot: bool = True,
                                   short_is_spot: bool = False) -> Optional[HedgingPair]:
        """执行跨交易所套保交易对

        Args:
            long_exchange: 多头交易所
            long_symbol: 多头交易对
            short_exchange: 空头交易所
            short_symbol: 空头交易对
            amount_usdt: 交易金额(USDT)
            long_is_spot: 多头是否为现货
            short_is_spot: 空头是否为现货

        Returns:
            HedgingPair: 套保交易对对象，失败返回None
        """
        try:
            # 检查交易所状态
            if not self._check_exchanges_ready([long_exchange, short_exchange]):
                logger.error(f"交易所状态检查失败: {long_exchange}, {short_exchange}")
                return None

            # 生成配对ID
            pair_id = f"{long_exchange}_{long_symbol}_{short_exchange}_{short_symbol}_{int(datetime.now().timestamp())}"

            # 获取当前价格
            long_price = await self._get_current_price(long_exchange, long_symbol, long_is_spot)
            short_price = await self._get_current_price(short_exchange, short_symbol, short_is_spot)

            if not long_price or not short_price:
                logger.error(f"获取价格失败: {long_exchange}={long_price}, {short_exchange}={short_price}")
                return None

            # 计算交易数量
            long_amount = amount_usdt / long_price
            short_amount = amount_usdt / short_price

            # 创建订单
            long_order = CrossExchangeOrder(
                exchange=long_exchange,
                symbol=long_symbol,
                side=OrderSide.BUY,
                order_type=OrderType.MARKET,
                amount=long_amount,
                price=long_price,
                is_spot=long_is_spot,
                timestamp=datetime.now(timezone.utc)
            )

            short_order = CrossExchangeOrder(
                exchange=short_exchange,
                symbol=short_symbol,
                side=OrderSide.SELL,
                order_type=OrderType.MARKET,
                amount=short_amount,
                price=short_price,
                is_spot=short_is_spot,
                timestamp=datetime.now(timezone.utc)
            )

            # 创建套保对
            hedging_pair = HedgingPair(
                long_order=long_order,
                short_order=short_order,
                pair_id=pair_id,
                created_at=datetime.now(timezone.utc),
                target_amount=amount_usdt
            )

            # 同时执行两个订单
            success = await self._execute_simultaneous_orders(hedging_pair)

            if success:
                self.hedging_pairs[pair_id] = hedging_pair
                logger.info(f"套保交易对执行成功: {pair_id}")

                # 发送成功通知
                message = (
                    f"跨交易所套保执行成功\n"
                    f"配对ID: {pair_id}\n"
                    f"多头: {long_exchange} {long_symbol} {long_amount:.6f}\n"
                    f"空头: {short_exchange} {short_symbol} {short_amount:.6f}\n"
                    f"目标金额: {amount_usdt:.2f} USDT"
                )
                self.ding_talk_manager.send_message(message, prefix="套保")

                return hedging_pair
            else:
                logger.error(f"套保交易对执行失败: {pair_id}")
                return None

        except Exception as e:
            logger.error(f"执行套保交易对失败: {str(e)}")
            return None

    async def _execute_simultaneous_orders(self, hedging_pair: HedgingPair) -> bool:
        """同时执行套保订单对

        Args:
            hedging_pair: 套保交易对

        Returns:
            bool: 是否执行成功
        """
        try:
            # 创建并发执行任务
            long_task = asyncio.create_task(
                self._execute_single_order(hedging_pair.long_order)
            )
            short_task = asyncio.create_task(
                self._execute_single_order(hedging_pair.short_order)
            )

            # 等待两个订单都完成，设置超时
            try:
                long_result, short_result = await asyncio.wait_for(
                    asyncio.gather(long_task, short_task, return_exceptions=True),
                    timeout=30.0  # 30秒超时
                )

                # 检查执行结果
                long_success = isinstance(long_result, bool) and long_result
                short_success = isinstance(short_result, bool) and short_result

                if long_success and short_success:
                    hedging_pair.status = "completed"
                    return True
                elif long_success or short_success:
                    hedging_pair.status = "partial"
                    # 处理部分成功的情况
                    await self._handle_partial_execution(hedging_pair, long_success, short_success)
                    return False
                else:
                    hedging_pair.status = "failed"
                    return False

            except asyncio.TimeoutError:
                logger.error(f"套保订单执行超时: {hedging_pair.pair_id}")
                hedging_pair.status = "failed"
                # 取消未完成的订单
                await self._cancel_pending_orders(hedging_pair)
                return False

        except Exception as e:
            logger.error(f"执行套保订单对失败: {str(e)}")
            hedging_pair.status = "failed"
            return False

    async def _execute_single_order(self, order: CrossExchangeOrder) -> bool:
        """执行单个订单

        Args:
            order: 订单对象

        Returns:
            bool: 是否执行成功
        """
        try:
            client = self.exchange_clients.get(order.exchange)
            if not client:
                order.status = "failed"
                order.error_message = f"未找到交易所客户端: {order.exchange}"
                return False

            # 根据订单类型执行
            if order.order_type == OrderType.MARKET:
                if order.is_spot:
                    result = await client.create_spot_market_order(
                        symbol=order.symbol,
                        side=order.side.value,
                        amount=order.amount
                    )
                else:
                    result = await client.create_futures_market_order(
                        symbol=order.symbol,
                        side=order.side.value,
                        amount=order.amount
                    )
            else:  # LIMIT
                if order.is_spot:
                    result = await client.create_spot_limit_order(
                        symbol=order.symbol,
                        side=order.side.value,
                        amount=order.amount,
                        price=order.price
                    )
                else:
                    result = await client.create_futures_limit_order(
                        symbol=order.symbol,
                        side=order.side.value,
                        amount=order.amount,
                        price=order.price
                    )

            if result and result.get('id'):
                order.order_id = result['id']
                order.status = "filled"
                order.filled_amount = result.get('filled', order.amount)
                order.filled_price = result.get('average', order.price)

                # 添加到活跃订单监控
                self.active_orders[order.order_id] = order

                logger.info(f"订单执行成功: {order.exchange} {order.symbol} {order.side.value} {order.amount}")
                return True
            else:
                order.status = "failed"
                order.error_message = "订单创建失败"
                return False

        except Exception as e:
            order.status = "failed"
            order.error_message = str(e)
            logger.error(f"执行订单失败: {order.exchange} {order.symbol} - {str(e)}")
            return False

    async def _get_current_price(self, exchange: str, symbol: str, is_spot: bool = True) -> Optional[float]:
        """获取当前价格

        Args:
            exchange: 交易所名称
            symbol: 交易对
            is_spot: 是否为现货

        Returns:
            float: 当前价格，失败返回None
        """
        try:
            client = self.exchange_clients.get(exchange)
            if not client:
                return None

            if is_spot:
                ticker = await client.fetch_spot_ticker(symbol)
            else:
                ticker = await client.fetch_futures_ticker(symbol)

            return ticker.get('last') if ticker else None

        except Exception as e:
            logger.error(f"获取价格失败: {exchange} {symbol} - {str(e)}")
            return None

    def _check_exchanges_ready(self, exchange_names: List[str]) -> bool:
        """检查交易所是否准备就绪

        Args:
            exchange_names: 交易所名称列表

        Returns:
            bool: 是否都准备就绪
        """
        for exchange_name in exchange_names:
            status = self.exchange_status.get(exchange_name, {})
            if not status.get('connected', False):
                logger.warning(f"交易所 {exchange_name} 未连接")
                return False

            # 检查错误计数
            if status.get('error_count', 0) > 5:
                logger.warning(f"交易所 {exchange_name} 错误次数过多")
                return False

        return True

    async def _monitor_exchange_status(self):
        """监控交易所状态"""
        while self.is_running:
            try:
                for exchange_name, client in self.exchange_clients.items():
                    try:
                        # 发送ping测试连接
                        start_time = datetime.now()
                        await client.ping()
                        end_time = datetime.now()

                        # 更新状态
                        self.exchange_status[exchange_name].update({
                            'connected': True,
                            'last_ping': end_time,
                            'error_count': 0,
                            'last_error': None
                        })

                        # 记录延迟
                        latency = (end_time - start_time).total_seconds() * 1000
                        self.latency_monitor[exchange_name].append(latency)

                        # 保持最近100次记录
                        if len(self.latency_monitor[exchange_name]) > 100:
                            self.latency_monitor[exchange_name] = self.latency_monitor[exchange_name][-100:]

                    except Exception as e:
                        # 更新错误状态
                        status = self.exchange_status[exchange_name]
                        status['connected'] = False
                        status['error_count'] = status.get('error_count', 0) + 1
                        status['last_error'] = str(e)

                        logger.warning(f"交易所 {exchange_name} 连接异常: {str(e)}")

                await asyncio.sleep(10)  # 每10秒检查一次

            except Exception as e:
                logger.error(f"监控交易所状态异常: {str(e)}")
                await asyncio.sleep(5)

    async def _monitor_network_latency(self):
        """监控网络延迟"""
        while self.is_running:
            try:
                # 计算平均延迟并报告异常
                for exchange_name, latencies in self.latency_monitor.items():
                    if latencies:
                        avg_latency = sum(latencies) / len(latencies)
                        max_latency = max(latencies)

                        # 如果延迟过高，发出警告
                        if avg_latency > 1000:  # 1秒
                            logger.warning(f"{exchange_name} 平均延迟过高: {avg_latency:.2f}ms")

                        if max_latency > 5000:  # 5秒
                            logger.warning(f"{exchange_name} 最大延迟过高: {max_latency:.2f}ms")

                await asyncio.sleep(60)  # 每分钟检查一次

            except Exception as e:
                logger.error(f"监控网络延迟异常: {str(e)}")
                await asyncio.sleep(30)

    async def _monitor_active_orders(self):
        """监控活跃订单"""
        while self.is_running:
            try:
                # 检查活跃订单状态
                orders_to_remove = []

                for order_id, order in self.active_orders.items():
                    try:
                        client = self.exchange_clients.get(order.exchange)
                        if client:
                            # 查询订单状态
                            order_status = await client.fetch_order(order_id, order.symbol)

                            if order_status:
                                # 更新订单状态
                                order.status = order_status.get('status', order.status)
                                order.filled_amount = order_status.get('filled', order.filled_amount)
                                order.filled_price = order_status.get('average', order.filled_price)

                                # 如果订单已完成，从活跃列表中移除
                                if order.status in ['filled', 'cancelled', 'rejected']:
                                    orders_to_remove.append(order_id)

                    except Exception as e:
                        logger.error(f"查询订单状态失败: {order_id} - {str(e)}")

                # 移除已完成的订单
                for order_id in orders_to_remove:
                    self.active_orders.pop(order_id, None)

                await asyncio.sleep(5)  # 每5秒检查一次

            except Exception as e:
                logger.error(f"监控活跃订单异常: {str(e)}")
                await asyncio.sleep(10)

    async def _handle_partial_execution(self, hedging_pair: HedgingPair, long_success: bool, short_success: bool):
        """处理部分执行的情况"""
        try:
            logger.warning(f"套保订单部分执行: {hedging_pair.pair_id}, 多头成功: {long_success}, 空头成功: {short_success}")

            # 发送警告消息
            message = (
                f"套保订单部分执行警告\n"
                f"配对ID: {hedging_pair.pair_id}\n"
                f"多头状态: {'成功' if long_success else '失败'}\n"
                f"空头状态: {'成功' if short_success else '失败'}\n"
                f"需要人工处理"
            )
            self.ding_talk_manager.send_message(message, prefix="套保警告")

            # TODO: 实现自动补单逻辑
            # 这里可以添加自动尝试补单的逻辑

        except Exception as e:
            logger.error(f"处理部分执行失败: {str(e)}")

    async def _cancel_pending_orders(self, hedging_pair: HedgingPair):
        """取消待处理的订单"""
        try:
            tasks = []

            # 取消多头订单
            if hedging_pair.long_order.order_id and hedging_pair.long_order.status == "pending":
                task = asyncio.create_task(
                    self._cancel_single_order(hedging_pair.long_order)
                )
                tasks.append(task)

            # 取消空头订单
            if hedging_pair.short_order.order_id and hedging_pair.short_order.status == "pending":
                task = asyncio.create_task(
                    self._cancel_single_order(hedging_pair.short_order)
                )
                tasks.append(task)

            if tasks:
                await asyncio.gather(*tasks, return_exceptions=True)

        except Exception as e:
            logger.error(f"取消订单失败: {str(e)}")

    async def _cancel_single_order(self, order: CrossExchangeOrder):
        """取消单个订单"""
        try:
            client = self.exchange_clients.get(order.exchange)
            if client and order.order_id:
                await client.cancel_order(order.order_id, order.symbol)
                order.status = "cancelled"
                logger.info(f"订单已取消: {order.exchange} {order.order_id}")

        except Exception as e:
            logger.error(f"取消订单失败: {order.exchange} {order.order_id} - {str(e)}")

    def get_exchange_status(self) -> Dict[str, Dict]:
        """获取所有交易所状态"""
        return self.exchange_status.copy()

    def get_hedging_pairs(self) -> Dict[str, HedgingPair]:
        """获取所有套保交易对"""
        return self.hedging_pairs.copy()

    def get_active_orders(self) -> Dict[str, CrossExchangeOrder]:
        """获取活跃订单"""
        return self.active_orders.copy()

    async def cleanup(self):
        """清理资源"""
        try:
            # 停止监控
            await self.stop_monitoring()

            # 取消所有活跃订单
            for hedging_pair in self.hedging_pairs.values():
                await self._cancel_pending_orders(hedging_pair)

            # 关闭所有交易所客户端
            for exchange_name, client in self.exchange_clients.items():
                try:
                    await client.close()
                    logger.info(f"已关闭 {exchange_name} 交易所客户端")
                except Exception as e:
                    logger.error(f"关闭 {exchange_name} 交易所客户端失败: {str(e)}")

            # 清空所有数据
            self.exchange_clients.clear()
            self.exchange_status.clear()
            self.active_orders.clear()
            self.hedging_pairs.clear()
            self.latency_monitor.clear()

            logger.info("跨交易所协调器清理完成")

        except Exception as e:
            logger.error(f"清理跨交易所协调器失败: {str(e)}")