import React, { useState } from 'react';
import {
  Card,
  Form,
  Input,
  Button,
  Checkbox,
  Select,
  InputNumber,
  Space,
  Row,
  Col,
  message
} from 'antd';
import {
  RiseOutlined,
  FallOutlined
} from '@ant-design/icons';

const { Option } = Select;

interface SingleOrderFormData {
  exchange: string;
  symbol: string;
  side: 'buy' | 'sell';
  order_type: 'market' | 'limit';
  amount: number;
  price?: number;
  is_spot: boolean;
}

interface SingleOrderFormProps {
  onSubmit: (data: SingleOrderFormData) => Promise<void>;
  loading?: boolean;
}

export const SingleOrderForm: React.FC<SingleOrderFormProps> = ({
  onSubmit,
  loading = false
}) => {
  const [form] = Form.useForm();
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [orderType, setOrderType] = useState<'market' | 'limit'>('market');
  const [side, setSide] = useState<'buy' | 'sell'>('buy');

  const handleSubmit = async (values: any) => {
    try {
      setSubmitStatus('idle');

      const formData: SingleOrderFormData = {
        exchange: values.exchange,
        symbol: values.symbol,
        side: values.side,
        order_type: values.order_type,
        amount: values.amount,
        price: values.price,
        is_spot: values.is_spot !== false
      };

      await onSubmit(formData);
      setSubmitStatus('success');
      message.success('订单提交成功！');

      // 3秒后重置状态
      setTimeout(() => setSubmitStatus('idle'), 3000);
    } catch (error) {
      setSubmitStatus('error');
      message.error(error instanceof Error ? error.message : '提交失败');
    }
  };

  return (
    <Card title="单独下单" style={{ maxWidth: 600, margin: '0 auto' }}>
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        initialValues={{
          side: 'buy',
          order_type: 'market',
          is_spot: true
        }}
        onValuesChange={(changedValues) => {
          if (changedValues.order_type) {
            setOrderType(changedValues.order_type);
          }
          if (changedValues.side) {
            setSide(changedValues.side);
          }
        }}
      >
        {/* 基础设置 */}
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              label="交易所"
              name="exchange"
              rules={[{ required: true, message: '请选择交易所' }]}
            >
              <Select placeholder="选择交易所">
                <Option value="binance">BINANCE</Option>
                <Option value="okx">OKX</Option>
              </Select>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label="交易对"
              name="symbol"
              rules={[{ required: true, message: '请选择交易对' }]}
            >
              <Select placeholder="选择交易对">
                <Option value="BTC/USDT">BTC/USDT</Option>
                <Option value="ETH/USDT">ETH/USDT</Option>
                <Option value="BNB/USDT">BNB/USDT</Option>
              </Select>
            </Form.Item>
          </Col>
        </Row>

        {/* 交易类型设置 */}
        <Form.Item name="is_spot" valuePropName="checked">
          <Checkbox>现货交易（取消勾选为合约交易）</Checkbox>
        </Form.Item>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              label="买卖方向"
              name="side"
              rules={[{ required: true, message: '请选择买卖方向' }]}
            >
              <Select>
                <Option value="buy">
                  <Space>
                    <RiseOutlined style={{ color: '#52c41a' }} />
                    买入
                  </Space>
                </Option>
                <Option value="sell">
                  <Space>
                    <FallOutlined style={{ color: '#ff4d4f' }} />
                    卖出
                  </Space>
                </Option>
              </Select>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label="订单类型"
              name="order_type"
              rules={[{ required: true, message: '请选择订单类型' }]}
            >
              <Select>
                <Option value="market">市价单</Option>
                <Option value="limit">限价单</Option>
              </Select>
            </Form.Item>
          </Col>
        </Row>

        {/* 数量和价格设置 */}
        <Row gutter={16}>
          <Col span={orderType === 'limit' ? 12 : 24}>
            <Form.Item
              label="数量"
              name="amount"
              rules={[{ required: true, message: '请输入交易数量' }]}
            >
              <InputNumber
                style={{ width: '100%' }}
                min={0}
                step={0.000001}
                placeholder="输入交易数量"
              />
            </Form.Item>
          </Col>
          {orderType === 'limit' && (
            <Col span={12}>
              <Form.Item
                label="价格"
                name="price"
                rules={[{ required: true, message: '限价单必须设置价格' }]}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  min={0}
                  step={0.01}
                  placeholder="输入限价"
                />
              </Form.Item>
            </Col>
          )}
        </Row>

        {/* 订单预览 */}
        <Card size="small" style={{ backgroundColor: '#f5f5f5', marginBottom: 16 }}>
          <h4 style={{ margin: '0 0 8px 0' }}>订单预览</h4>
          <Row gutter={[16, 8]}>
            <Col span={12}>
              <span>交易所: </span>
              <strong>{form.getFieldValue('exchange')?.toUpperCase() || '-'}</strong>
            </Col>
            <Col span={12}>
              <span>交易对: </span>
              <strong>{form.getFieldValue('symbol') || '-'}</strong>
            </Col>
            <Col span={12}>
              <span>类型: </span>
              <strong>{form.getFieldValue('is_spot') !== false ? '现货' : '合约'}</strong>
            </Col>
            <Col span={12}>
              <span>方向: </span>
              <strong style={{ color: side === 'buy' ? '#52c41a' : '#ff4d4f' }}>
                {side === 'buy' ? '买入' : '卖出'}
              </strong>
            </Col>
            <Col span={12}>
              <span>订单类型: </span>
              <strong>{orderType === 'market' ? '市价单' : '限价单'}</strong>
            </Col>
            <Col span={12}>
              <span>数量: </span>
              <strong>{form.getFieldValue('amount') || 0}</strong>
            </Col>
            {orderType === 'limit' && (
              <Col span={12}>
                <span>价格: </span>
                <strong>{form.getFieldValue('price') || '-'}</strong>
              </Col>
            )}
          </Row>
        </Card>

        {/* 提交按钮 */}
        <Form.Item style={{ textAlign: 'center' }}>
          <Space>
            <Button onClick={() => form.resetFields()}>
              重置
            </Button>
            <Button
              type="primary"
              htmlType="submit"
              loading={loading}
              disabled={submitStatus === 'success'}
              style={{
                backgroundColor: side === 'buy' ? '#52c41a' : '#ff4d4f',
                borderColor: side === 'buy' ? '#52c41a' : '#ff4d4f'
              }}
            >
              {side === 'buy' ? <RiseOutlined /> : <FallOutlined />}
              {submitStatus === 'success' ? '已提交' : `${side === 'buy' ? '买入' : '卖出'}订单`}
            </Button>
          </Space>
        </Form.Item>
      </Form>
    </Card>
  );
};
