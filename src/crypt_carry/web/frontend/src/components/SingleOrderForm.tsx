import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Loader2, AlertCircle, CheckCircle2, TrendingUp, TrendingDown } from 'lucide-react';

// 表单验证模式
const singleOrderSchema = z.object({
  exchange: z.string().min(1, "请选择交易所"),
  symbol: z.string().min(1, "请选择交易对"),
  side: z.enum(['buy', 'sell'], {
    required_error: "请选择买卖方向"
  }),
  order_type: z.enum(['market', 'limit'], {
    required_error: "请选择订单类型"
  }),
  amount: z.number().positive("数量必须大于0"),
  price: z.number().optional(),
  is_spot: z.boolean()
}).refine((data) => {
  // 限价单必须有价格
  if (data.order_type === 'limit' && (!data.price || data.price <= 0)) {
    return false;
  }
  return true;
}, {
  message: "限价单必须设置价格",
  path: ["price"]
});

type SingleOrderForm = z.infer<typeof singleOrderSchema>;

interface SingleOrderFormProps {
  onSubmit: (data: SingleOrderForm) => Promise<void>;
  loading?: boolean;
}

export const SingleOrderForm: React.FC<SingleOrderFormProps> = ({
  onSubmit,
  loading = false
}) => {
  const [exchanges, setExchanges] = useState<string[]>([]);
  const [symbols, setSymbols] = useState<string[]>([]);
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [errorMessage, setErrorMessage] = useState<string>('');

  const form = useForm<SingleOrderForm>({
    resolver: zodResolver(singleOrderSchema),
    defaultValues: {
      exchange: '',
      symbol: '',
      side: 'buy',
      order_type: 'market',
      amount: 0,
      price: undefined,
      is_spot: true
    }
  });

  const watchOrderType = form.watch('order_type');
  const watchSide = form.watch('side');

  // 加载配置数据
  useEffect(() => {
    const loadConfigData = async () => {
      try {
        // 加载交易所列表
        const exchangesRes = await fetch('/api/hedging/config/exchanges');
        const exchangesData = await exchangesRes.json();
        if (exchangesData.success) {
          setExchanges(exchangesData.exchanges);
        }

        // 加载交易对列表
        const symbolsRes = await fetch('/api/hedging/config/symbols');
        const symbolsData = await symbolsRes.json();
        if (symbolsData.success) {
          setSymbols(symbolsData.symbols);
        }
      } catch (error) {
        console.error('加载配置数据失败:', error);
      }
    };

    loadConfigData();
  }, []);

  // 处理表单提交
  const handleSubmit = async (data: SingleOrderForm) => {
    try {
      setSubmitStatus('idle');
      setErrorMessage('');
      await onSubmit(data);
      setSubmitStatus('success');
      // 3秒后重置状态
      setTimeout(() => setSubmitStatus('idle'), 3000);
    } catch (error) {
      setSubmitStatus('error');
      setErrorMessage(error instanceof Error ? error.message : '提交失败');
    }
  };

  // 获取买卖方向的颜色和图标
  const getSideDisplay = (side: string) => {
    if (side === 'buy') {
      return {
        color: 'text-green-600',
        bgColor: 'bg-green-50',
        icon: <TrendingUp className="w-4 h-4" />,
        label: '买入'
      };
    } else {
      return {
        color: 'text-red-600',
        bgColor: 'bg-red-50',
        icon: <TrendingDown className="w-4 h-4" />,
        label: '卖出'
      };
    }
  };

  const sideDisplay = getSideDisplay(watchSide);

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          单独下单
          {submitStatus === 'success' && (
            <Badge variant="default" className="bg-green-500">
              <CheckCircle2 className="w-3 h-3 mr-1" />
              提交成功
            </Badge>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
            
            {/* 基础设置 */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="exchange"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>交易所</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="选择交易所" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {exchanges.map(exchange => (
                          <SelectItem key={exchange} value={exchange}>
                            {exchange.toUpperCase()}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="symbol"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>交易对</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="选择交易对" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {symbols.map(symbol => (
                          <SelectItem key={symbol} value={symbol}>
                            {symbol}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* 交易类型设置 */}
            <div className="space-y-4">
              <FormField
                control={form.control}
                name="is_spot"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center space-x-3 space-y-0">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel>现货交易</FormLabel>
                      <p className="text-sm text-muted-foreground">
                        取消勾选为合约交易
                      </p>
                    </div>
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="side"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>买卖方向</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger className={sideDisplay.bgColor}>
                            <SelectValue />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="buy">
                            <div className="flex items-center gap-2 text-green-600">
                              <TrendingUp className="w-4 h-4" />
                              买入
                            </div>
                          </SelectItem>
                          <SelectItem value="sell">
                            <div className="flex items-center gap-2 text-red-600">
                              <TrendingDown className="w-4 h-4" />
                              卖出
                            </div>
                          </SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="order_type"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>订单类型</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="market">市价单</SelectItem>
                          <SelectItem value="limit">限价单</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            {/* 数量和价格设置 */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="amount"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>数量</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        step="0.000001"
                        placeholder="输入交易数量"
                        {...field}
                        onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {watchOrderType === 'limit' && (
                <FormField
                  control={form.control}
                  name="price"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>价格</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          step="0.01"
                          placeholder="输入限价"
                          value={field.value || ''}
                          onChange={(e) => field.onChange(parseFloat(e.target.value) || undefined)}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              )}
            </div>

            {/* 订单预览 */}
            <Card className="p-4 bg-gray-50">
              <h4 className="font-medium mb-2">订单预览</h4>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>交易所:</span>
                  <span className="font-medium">{form.watch('exchange')?.toUpperCase() || '-'}</span>
                </div>
                <div className="flex justify-between">
                  <span>交易对:</span>
                  <span className="font-medium">{form.watch('symbol') || '-'}</span>
                </div>
                <div className="flex justify-between">
                  <span>类型:</span>
                  <span className="font-medium">
                    {form.watch('is_spot') ? '现货' : '合约'}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>方向:</span>
                  <span className={`font-medium flex items-center gap-1 ${sideDisplay.color}`}>
                    {sideDisplay.icon}
                    {sideDisplay.label}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>订单类型:</span>
                  <span className="font-medium">
                    {watchOrderType === 'market' ? '市价单' : '限价单'}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>数量:</span>
                  <span className="font-medium">{form.watch('amount') || 0}</span>
                </div>
                {watchOrderType === 'limit' && (
                  <div className="flex justify-between">
                    <span>价格:</span>
                    <span className="font-medium">{form.watch('price') || '-'}</span>
                  </div>
                )}
              </div>
            </Card>

            {/* 错误提示 */}
            {submitStatus === 'error' && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>{errorMessage}</AlertDescription>
              </Alert>
            )}

            {/* 提交按钮 */}
            <div className="flex justify-end space-x-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => form.reset()}
              >
                重置
              </Button>
              <Button
                type="submit"
                disabled={loading || submitStatus === 'success'}
                className={watchSide === 'buy' ? 'bg-green-600 hover:bg-green-700' : 'bg-red-600 hover:bg-red-700'}
              >
                {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                {sideDisplay.icon}
                {submitStatus === 'success' ? '已提交' : `${sideDisplay.label}订单`}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
};
