import React, { useState } from 'react';
import {
  Card,
  Form,
  Button,
  Checkbox,
  Select,
  InputNumber,
  Space,
  Row,
  Col,
  message
} from 'antd';

const { Option } = Select;

interface HedgingOrderFormData {
  conditions: Array<{
    type: string;
    enabled: boolean;
    trigger_value: number;
    comparison_operator: string;
  }>;
  long_exchange: string;
  long_symbol: string;
  long_is_spot: boolean;
  short_exchange: string;
  short_symbol: string;
  short_is_spot: boolean;
  amount_usdt: number;
  priority: number;
}

interface HedgingOrderFormProps {
  onSubmit: (data: HedgingOrderFormData) => Promise<void>;
  loading?: boolean;
}

export const HedgingOrderForm: React.FC<HedgingOrderFormProps> = ({
  onSubmit,
  loading = false
}) => {
  const [form] = Form.useForm();
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');

  const handleSubmit = async (values: any) => {
    try {
      setSubmitStatus('idle');

      // 验证至少选择一个套保条件
      const hasCondition = values.price_difference_enabled ||
                          values.funding_rate_enabled ||
                          values.basis_rate_enabled;

      if (!hasCondition) {
        message.error('请至少选择一个套保触发条件');
        return;
      }

      // 构造提交数据
      const formData: HedgingOrderFormData = {
        conditions: [
          {
            type: 'price_difference',
            enabled: values.price_difference_enabled || false,
            trigger_value: values.price_difference_value || 100,
            comparison_operator: '>'
          },
          {
            type: 'funding_rate_diff',
            enabled: values.funding_rate_enabled || false,
            trigger_value: values.funding_rate_value || 0.001,
            comparison_operator: '>'
          },
          {
            type: 'basis_rate',
            enabled: values.basis_rate_enabled || false,
            trigger_value: values.basis_rate_value || 0.005,
            comparison_operator: '>'
          }
        ],
        long_exchange: values.long_exchange,
        long_symbol: values.long_symbol,
        long_is_spot: values.long_is_spot !== false,
        short_exchange: values.short_exchange,
        short_symbol: values.short_symbol,
        short_is_spot: values.short_is_spot === true,
        amount_usdt: values.amount_usdt,
        priority: values.priority || 1
      };

      await onSubmit(formData);
      setSubmitStatus('success');
      message.success('套保订单创建成功！');

      // 3秒后重置状态
      setTimeout(() => setSubmitStatus('idle'), 3000);
    } catch (error) {
      setSubmitStatus('error');
      message.error(error instanceof Error ? error.message : '提交失败');
    }
  };

  return (
    <div style={{ padding: '16px' }}>
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        initialValues={{
          amount_usdt: 1000,
          priority: 1,
          price_difference_value: 100,
          funding_rate_value: 0.001,
          basis_rate_value: 0.005
        }}
      >
        <Row gutter={24}>
          {/* 左侧：套保条件设置 */}
          <Col span={12}>
            <Card title="套保条件设置" size="small" style={{ height: '100%' }}>
              <p style={{ color: '#666', marginBottom: 12, fontSize: '12px' }}>
                选择触发条件，满足任一条件时执行套保
              </p>

              <Space direction="vertical" style={{ width: '100%' }} size="small">
                {/* 价差套保 */}
                <Row gutter={8} align="middle">
                  <Col span={8}>
                    <Form.Item name="price_difference_enabled" valuePropName="checked" style={{ margin: 0 }}>
                      <Checkbox>价差套保</Checkbox>
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item
                      name="price_difference_value"
                      style={{ margin: 0 }}
                      rules={[
                        ({ getFieldValue }) => ({
                          validator(_, value) {
                            if (getFieldValue('price_difference_enabled') && (!value || value <= 0)) {
                              return Promise.reject(new Error('请输入有效的价差触发值'));
                            }
                            return Promise.resolve();
                          },
                        }),
                      ]}
                    >
                      <InputNumber
                        placeholder="触发值"
                        addonAfter="USDT"
                        size="small"
                        min={0}
                        style={{ width: '100%' }}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <span style={{ color: '#666', fontSize: '11px' }}>
                      价差超过设定值触发
                    </span>
                  </Col>
                </Row>

                {/* 资金费率差异 */}
                <Row gutter={8} align="middle">
                  <Col span={8}>
                    <Form.Item name="funding_rate_enabled" valuePropName="checked" style={{ margin: 0 }}>
                      <Checkbox>资金费率差异</Checkbox>
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item
                      name="funding_rate_value"
                      style={{ margin: 0 }}
                      rules={[
                        ({ getFieldValue }) => ({
                          validator(_, value) {
                            if (getFieldValue('funding_rate_enabled') && (!value || value <= 0)) {
                              return Promise.reject(new Error('请输入有效的资金费率触发值'));
                            }
                            return Promise.resolve();
                          },
                        }),
                      ]}
                    >
                      <InputNumber
                        placeholder="触发值"
                        step={0.0001}
                        addonAfter="%"
                        size="small"
                        min={0}
                        style={{ width: '100%' }}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <span style={{ color: '#666', fontSize: '11px' }}>
                      费率差异超过设定值触发
                    </span>
                  </Col>
                </Row>

                {/* 基差率 */}
                <Row gutter={8} align="middle">
                  <Col span={8}>
                    <Form.Item name="basis_rate_enabled" valuePropName="checked" style={{ margin: 0 }}>
                      <Checkbox>基差率</Checkbox>
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item
                      name="basis_rate_value"
                      style={{ margin: 0 }}
                      rules={[
                        ({ getFieldValue }) => ({
                          validator(_, value) {
                            if (getFieldValue('basis_rate_enabled') && (!value || value <= 0)) {
                              return Promise.reject(new Error('请输入有效的基差率触发值'));
                            }
                            return Promise.resolve();
                          },
                        }),
                      ]}
                    >
                      <InputNumber
                        placeholder="触发值"
                        step={0.0001}
                        addonAfter="%"
                        size="small"
                        min={0}
                        style={{ width: '100%' }}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <span style={{ color: '#666', fontSize: '11px' }}>
                      基差率超过设定值触发
                    </span>
                  </Col>
                </Row>
              </Space>
            </Card>
          </Col>

          {/* 右侧：交易设置 */}
          <Col span={12}>
            <Card title="交易设置" size="small" style={{ height: '100%' }}>
              <Row gutter={16}>
                {/* 多头设置 */}
                <Col span={12}>
                  <div style={{ border: '1px solid #d9d9d9', borderRadius: '6px', padding: '12px', marginBottom: '12px' }}>
                    <h4 style={{ margin: '0 0 8px 0', color: '#52c41a', fontSize: '14px' }}>多头设置</h4>

                    <Form.Item
                      label="交易所"
                      name="long_exchange"
                      rules={[{ required: true, message: '请选择多头交易所' }]}
                      style={{ marginBottom: 8 }}
                    >
                      <Select placeholder="选择交易所" size="small">
                        <Option value="binance">BINANCE</Option>
                        <Option value="okx">OKX</Option>
                      </Select>
                    </Form.Item>

                    <Form.Item
                      label="交易对"
                      name="long_symbol"
                      rules={[{ required: true, message: '请选择多头交易对' }]}
                      style={{ marginBottom: 8 }}
                    >
                      <Select placeholder="选择交易对" size="small">
                        <Option value="BTC/USDT">BTC/USDT</Option>
                        <Option value="ETH/USDT">ETH/USDT</Option>
                        <Option value="BNB/USDT">BNB/USDT</Option>
                      </Select>
                    </Form.Item>

                    <Form.Item
                      label="交易类型"
                      name="long_is_spot"
                      rules={[{ required: true, message: '请选择多头交易类型' }]}
                      style={{ margin: 0 }}
                    >
                      <Select placeholder="选择交易类型" size="small">
                        <Option value={true}>现货交易</Option>
                        <Option value={false}>合约交易</Option>
                      </Select>
                    </Form.Item>
                  </div>
                </Col>

                {/* 空头设置 */}
                <Col span={12}>
                  <div style={{ border: '1px solid #d9d9d9', borderRadius: '6px', padding: '12px', marginBottom: '12px' }}>
                    <h4 style={{ margin: '0 0 8px 0', color: '#ff4d4f', fontSize: '14px' }}>空头设置</h4>

                    <Form.Item
                      label="交易所"
                      name="short_exchange"
                      rules={[{ required: true, message: '请选择空头交易所' }]}
                      style={{ marginBottom: 8 }}
                    >
                      <Select placeholder="选择交易所" size="small">
                        <Option value="binance">BINANCE</Option>
                        <Option value="okx">OKX</Option>
                      </Select>
                    </Form.Item>

                    <Form.Item
                      label="交易对"
                      name="short_symbol"
                      rules={[{ required: true, message: '请选择空头交易对' }]}
                      style={{ marginBottom: 8 }}
                    >
                      <Select placeholder="选择交易对" size="small">
                        <Option value="BTC/USDT">BTC/USDT</Option>
                        <Option value="ETH/USDT">ETH/USDT</Option>
                        <Option value="BNB/USDT">BNB/USDT</Option>
                      </Select>
                    </Form.Item>

                    <Form.Item
                      label="交易类型"
                      name="short_is_spot"
                      rules={[{ required: true, message: '请选择空头交易类型' }]}
                      style={{ margin: 0 }}
                    >
                      <Select placeholder="选择交易类型" size="small">
                        <Option value={true}>现货交易</Option>
                        <Option value={false}>合约交易</Option>
                      </Select>
                    </Form.Item>
                  </div>
                </Col>
              </Row>

              {/* 其他设置 */}
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    label="交易金额 (USDT)"
                    name="amount_usdt"
                    rules={[{ required: true, message: '请输入交易金额' }]}
                    style={{ marginBottom: 8 }}
                  >
                    <InputNumber
                      style={{ width: '100%' }}
                      size="small"
                      min={1}
                      placeholder="输入交易金额"
                    />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    label="优先级 (1-10)"
                    name="priority"
                    style={{ marginBottom: 8 }}
                  >
                    <InputNumber
                      style={{ width: '100%' }}
                      size="small"
                      min={1}
                      max={10}
                      placeholder="输入优先级"
                    />
                  </Form.Item>
                </Col>
              </Row>

              {/* 提交按钮 */}
              <div style={{ textAlign: 'center', marginTop: 16 }}>
                <Space>
                  <Button size="small" onClick={() => form.resetFields()}>
                    重置
                  </Button>
                  <Button
                    type="primary"
                    size="small"
                    htmlType="submit"
                    loading={loading}
                    disabled={submitStatus === 'success'}
                  >
                    {submitStatus === 'success' ? '已提交' : '创建套保订单'}
                  </Button>
                </Space>
              </div>
            </Card>
          </Col>
        </Row>
      </Form>
    </div>
  );
};
