import React, { useState, useEffect } from 'react';
import {
  Card,
  Form,
  Input,
  Button,
  Checkbox,
  Select,
  InputNumber,
  Alert,
  Space,
  Divider,
  Row,
  Col,
  message
} from 'antd';

const { Option } = Select;

interface HedgingOrderFormData {
  conditions: Array<{
    type: string;
    enabled: boolean;
    trigger_value: number;
    comparison_operator: string;
  }>;
  long_exchange: string;
  long_symbol: string;
  long_is_spot: boolean;
  short_exchange: string;
  short_symbol: string;
  short_is_spot: boolean;
  amount_usdt: number;
  priority: number;
}

interface HedgingOrderFormProps {
  onSubmit: (data: HedgingOrderFormData) => Promise<void>;
  loading?: boolean;
}

export const HedgingOrderForm: React.FC<HedgingOrderFormProps> = ({
  onSubmit,
  loading = false
}) => {
  const [form] = Form.useForm();
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');

  const handleSubmit = async (values: any) => {
    try {
      setSubmitStatus('idle');

      // 构造提交数据
      const formData: HedgingOrderFormData = {
        conditions: [
          {
            type: 'price_difference',
            enabled: values.price_difference_enabled || false,
            trigger_value: values.price_difference_value || 100,
            comparison_operator: '>'
          },
          {
            type: 'funding_rate_diff',
            enabled: values.funding_rate_enabled || false,
            trigger_value: values.funding_rate_value || 0.001,
            comparison_operator: '>'
          },
          {
            type: 'basis_rate',
            enabled: values.basis_rate_enabled || false,
            trigger_value: values.basis_rate_value || 0.005,
            comparison_operator: '>'
          }
        ],
        long_exchange: values.long_exchange,
        long_symbol: values.long_symbol,
        long_is_spot: values.long_is_spot !== false,
        short_exchange: values.short_exchange,
        short_symbol: values.short_symbol,
        short_is_spot: values.short_is_spot === true,
        amount_usdt: values.amount_usdt,
        priority: values.priority || 1
      };

      await onSubmit(formData);
      setSubmitStatus('success');
      message.success('套保订单创建成功！');

      // 3秒后重置状态
      setTimeout(() => setSubmitStatus('idle'), 3000);
    } catch (error) {
      setSubmitStatus('error');
      message.error(error instanceof Error ? error.message : '提交失败');
    }
  };

  return (
    <Card title="套保订单设置" style={{ maxWidth: 800, margin: '0 auto' }}>
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        initialValues={{
          long_is_spot: true,
          short_is_spot: false,
          amount_usdt: 1000,
          priority: 1,
          price_difference_value: 100,
          funding_rate_value: 0.001,
          basis_rate_value: 0.005
        }}
      >
        {/* 套保条件设置 */}
        <Card type="inner" title="套保条件设置" style={{ marginBottom: 16 }}>
          <p style={{ color: '#666', marginBottom: 16 }}>
            选择一个或多个触发条件，当任一条件满足时将执行套保操作
          </p>

          <Space direction="vertical" style={{ width: '100%' }}>
            {/* 价差套保 */}
            <Card size="small">
              <Row gutter={16} align="middle">
                <Col span={6}>
                  <Form.Item name="price_difference_enabled" valuePropName="checked" style={{ margin: 0 }}>
                    <Checkbox>价差套保</Checkbox>
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item name="price_difference_value" style={{ margin: 0 }}>
                    <InputNumber
                      placeholder="触发值"
                      addonAfter="USDT"
                      style={{ width: '100%' }}
                    />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <span style={{ color: '#666', fontSize: '12px' }}>
                    当两个交易所同一交易对价差超过设定值时触发
                  </span>
                </Col>
              </Row>
            </Card>

            {/* 资金费率差异 */}
            <Card size="small">
              <Row gutter={16} align="middle">
                <Col span={6}>
                  <Form.Item name="funding_rate_enabled" valuePropName="checked" style={{ margin: 0 }}>
                    <Checkbox>资金费率差异</Checkbox>
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item name="funding_rate_value" style={{ margin: 0 }}>
                    <InputNumber
                      placeholder="触发值"
                      step={0.0001}
                      addonAfter="%"
                      style={{ width: '100%' }}
                    />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <span style={{ color: '#666', fontSize: '12px' }}>
                    当两个交易所资金费率差异超过设定值时触发
                  </span>
                </Col>
              </Row>
            </Card>

            {/* 基差率 */}
            <Card size="small">
              <Row gutter={16} align="middle">
                <Col span={6}>
                  <Form.Item name="basis_rate_enabled" valuePropName="checked" style={{ margin: 0 }}>
                    <Checkbox>基差率</Checkbox>
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item name="basis_rate_value" style={{ margin: 0 }}>
                    <InputNumber
                      placeholder="触发值"
                      step={0.0001}
                      addonAfter="%"
                      style={{ width: '100%' }}
                    />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <span style={{ color: '#666', fontSize: '12px' }}>
                    当现货和期货基差率超过设定值时触发
                  </span>
                </Col>
              </Row>
            </Card>
          </Space>
        </Card>

        <Divider />

        {/* 交易设置 */}
        <Row gutter={24}>
          {/* 多头设置 */}
          <Col span={12}>
            <Card type="inner" title={<span style={{ color: '#52c41a' }}>多头设置</span>}>
              <Form.Item
                label="交易所"
                name="long_exchange"
                rules={[{ required: true, message: '请选择交易所' }]}
              >
                <Select placeholder="选择交易所">
                  <Option value="binance">BINANCE</Option>
                  <Option value="okx">OKX</Option>
                </Select>
              </Form.Item>

              <Form.Item
                label="交易对"
                name="long_symbol"
                rules={[{ required: true, message: '请选择交易对' }]}
              >
                <Select placeholder="选择交易对">
                  <Option value="BTC/USDT">BTC/USDT</Option>
                  <Option value="ETH/USDT">ETH/USDT</Option>
                  <Option value="BNB/USDT">BNB/USDT</Option>
                </Select>
              </Form.Item>

              <Form.Item name="long_is_spot" valuePropName="checked">
                <Checkbox>现货交易</Checkbox>
              </Form.Item>
            </Card>
          </Col>

          {/* 空头设置 */}
          <Col span={12}>
            <Card type="inner" title={<span style={{ color: '#ff4d4f' }}>空头设置</span>}>
              <Form.Item
                label="交易所"
                name="short_exchange"
                rules={[{ required: true, message: '请选择交易所' }]}
              >
                <Select placeholder="选择交易所">
                  <Option value="binance">BINANCE</Option>
                  <Option value="okx">OKX</Option>
                </Select>
              </Form.Item>

              <Form.Item
                label="交易对"
                name="short_symbol"
                rules={[{ required: true, message: '请选择交易对' }]}
              >
                <Select placeholder="选择交易对">
                  <Option value="BTC/USDT">BTC/USDT</Option>
                  <Option value="ETH/USDT">ETH/USDT</Option>
                  <Option value="BNB/USDT">BNB/USDT</Option>
                </Select>
              </Form.Item>

              <Form.Item name="short_is_spot" valuePropName="checked">
                <Checkbox>现货交易</Checkbox>
              </Form.Item>
            </Card>
          </Col>
        </Row>

        <Divider />

        {/* 其他设置 */}
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              label="交易金额 (USDT)"
              name="amount_usdt"
              rules={[{ required: true, message: '请输入交易金额' }]}
            >
              <InputNumber
                style={{ width: '100%' }}
                min={1}
                placeholder="输入交易金额"
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label="优先级 (1-10)"
              name="priority"
            >
              <InputNumber
                style={{ width: '100%' }}
                min={1}
                max={10}
                placeholder="输入优先级"
              />
            </Form.Item>
          </Col>
        </Row>

        {/* 提交按钮 */}
        <Form.Item style={{ textAlign: 'center', marginTop: 24 }}>
          <Space>
            <Button onClick={() => form.resetFields()}>
              重置
            </Button>
            <Button
              type="primary"
              htmlType="submit"
              loading={loading}
              disabled={submitStatus === 'success'}
            >
              {submitStatus === 'success' ? '已提交' : '创建套保订单'}
            </Button>
          </Space>
        </Form.Item>
      </Form>
    </Card>
  );
};
