import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Loader2, AlertCircle, CheckCircle2 } from 'lucide-react';

// 条件类型定义
interface ConditionType {
  type: string;
  label: string;
  description: string;
  defaultValue: number;
  unit: string;
}

const conditionTypes: ConditionType[] = [
  {
    type: 'price_difference',
    label: '价差套保',
    description: '当两个交易所同一交易对价差超过设定值时触发',
    defaultValue: 100,
    unit: 'USDT'
  },
  {
    type: 'funding_rate_diff',
    label: '资金费率差异',
    description: '当两个交易所资金费率差异超过设定值时触发',
    defaultValue: 0.001,
    unit: '%'
  },
  {
    type: 'basis_rate',
    label: '基差率',
    description: '当现货和期货基差率超过设定值时触发',
    defaultValue: 0.005,
    unit: '%'
  }
];

// 表单验证模式
const hedgingOrderSchema = z.object({
  conditions: z.array(z.object({
    type: z.string(),
    enabled: z.boolean(),
    trigger_value: z.number().positive(),
    comparison_operator: z.string()
  })).refine(data => data.some(c => c.enabled), {
    message: "至少需要启用一个条件"
  }),
  long_exchange: z.string().min(1, "请选择多头交易所"),
  long_symbol: z.string().min(1, "请选择多头交易对"),
  long_is_spot: z.boolean(),
  short_exchange: z.string().min(1, "请选择空头交易所"),
  short_symbol: z.string().min(1, "请选择空头交易对"),
  short_is_spot: z.boolean(),
  amount_usdt: z.number().positive("交易金额必须大于0"),
  priority: z.number().int().min(1).max(10)
});

type HedgingOrderForm = z.infer<typeof hedgingOrderSchema>;

interface HedgingOrderFormProps {
  onSubmit: (data: HedgingOrderForm) => Promise<void>;
  loading?: boolean;
}

export const HedgingOrderForm: React.FC<HedgingOrderFormProps> = ({
  onSubmit,
  loading = false
}) => {
  const [exchanges, setExchanges] = useState<string[]>([]);
  const [symbols, setSymbols] = useState<string[]>([]);
  const [templates, setTemplates] = useState<any>({});
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [errorMessage, setErrorMessage] = useState<string>('');

  const form = useForm<HedgingOrderForm>({
    resolver: zodResolver(hedgingOrderSchema),
    defaultValues: {
      conditions: conditionTypes.map(ct => ({
        type: ct.type,
        enabled: false,
        trigger_value: ct.defaultValue,
        comparison_operator: '>'
      })),
      long_exchange: '',
      long_symbol: '',
      long_is_spot: true,
      short_exchange: '',
      short_symbol: '',
      short_is_spot: false,
      amount_usdt: 1000,
      priority: 1
    }
  });

  // 加载配置数据
  useEffect(() => {
    const loadConfigData = async () => {
      try {
        // 加载交易所列表
        const exchangesRes = await fetch('/api/hedging/config/exchanges');
        const exchangesData = await exchangesRes.json();
        if (exchangesData.success) {
          setExchanges(exchangesData.exchanges);
        }

        // 加载交易对列表
        const symbolsRes = await fetch('/api/hedging/config/symbols');
        const symbolsData = await symbolsRes.json();
        if (symbolsData.success) {
          setSymbols(symbolsData.symbols);
        }

        // 加载模板配置
        const templatesRes = await fetch('/api/hedging/config/templates');
        const templatesData = await templatesRes.json();
        if (templatesData.success) {
          setTemplates(templatesData.templates);
        }
      } catch (error) {
        console.error('加载配置数据失败:', error);
      }
    };

    loadConfigData();
  }, []);

  // 应用模板
  const applyTemplate = (conditionType: string, symbol: string) => {
    const template = templates[conditionType]?.[symbol] || templates[conditionType]?.DEFAULT;
    if (template) {
      const conditions = form.getValues('conditions');
      const updatedConditions = conditions.map(c => 
        c.type === conditionType 
          ? { ...c, trigger_value: template.TRIGGER_VALUE }
          : c
      );
      form.setValue('conditions', updatedConditions);
    }
  };

  // 处理表单提交
  const handleSubmit = async (data: HedgingOrderForm) => {
    try {
      setSubmitStatus('idle');
      setErrorMessage('');
      await onSubmit(data);
      setSubmitStatus('success');
      // 3秒后重置状态
      setTimeout(() => setSubmitStatus('idle'), 3000);
    } catch (error) {
      setSubmitStatus('error');
      setErrorMessage(error instanceof Error ? error.message : '提交失败');
    }
  };

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          套保订单设置
          {submitStatus === 'success' && (
            <Badge variant="default" className="bg-green-500">
              <CheckCircle2 className="w-3 h-3 mr-1" />
              提交成功
            </Badge>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
            
            {/* 套保条件设置 */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">套保条件设置</h3>
              <p className="text-sm text-muted-foreground">
                选择一个或多个触发条件，当任一条件满足时将执行套保操作
              </p>
              
              <FormField
                control={form.control}
                name="conditions"
                render={({ field }) => (
                  <FormItem>
                    <div className="space-y-3">
                      {conditionTypes.map((conditionType, index) => (
                        <Card key={conditionType.type} className="p-4">
                          <div className="flex items-start space-x-4">
                            <FormControl>
                              <Checkbox
                                checked={field.value[index]?.enabled || false}
                                onCheckedChange={(checked) => {
                                  const newConditions = [...field.value];
                                  newConditions[index] = {
                                    ...newConditions[index],
                                    enabled: !!checked
                                  };
                                  field.onChange(newConditions);
                                }}
                              />
                            </FormControl>
                            
                            <div className="flex-1 space-y-2">
                              <div>
                                <h4 className="font-medium">{conditionType.label}</h4>
                                <p className="text-sm text-muted-foreground">
                                  {conditionType.description}
                                </p>
                              </div>
                              
                              {field.value[index]?.enabled && (
                                <div className="flex items-center space-x-2">
                                  <Select
                                    value={field.value[index]?.comparison_operator || '>'}
                                    onValueChange={(value) => {
                                      const newConditions = [...field.value];
                                      newConditions[index] = {
                                        ...newConditions[index],
                                        comparison_operator: value
                                      };
                                      field.onChange(newConditions);
                                    }}
                                  >
                                    <SelectTrigger className="w-20">
                                      <SelectValue />
                                    </SelectTrigger>
                                    <SelectContent>
                                      <SelectItem value=">">></SelectItem>
                                      <SelectItem value=">=">>=</SelectItem>
                                      <SelectItem value="<"><</SelectItem>
                                      <SelectItem value="<="><=</SelectItem>
                                    </SelectContent>
                                  </Select>
                                  
                                  <Input
                                    type="number"
                                    step={conditionType.type === 'price_difference' ? '1' : '0.0001'}
                                    value={field.value[index]?.trigger_value || conditionType.defaultValue}
                                    onChange={(e) => {
                                      const newConditions = [...field.value];
                                      newConditions[index] = {
                                        ...newConditions[index],
                                        trigger_value: parseFloat(e.target.value) || 0
                                      };
                                      field.onChange(newConditions);
                                    }}
                                    className="w-32"
                                  />
                                  
                                  <span className="text-sm text-muted-foreground">
                                    {conditionType.unit}
                                  </span>
                                  
                                  <Button
                                    type="button"
                                    variant="outline"
                                    size="sm"
                                    onClick={() => applyTemplate(conditionType.type, form.getValues('long_symbol'))}
                                  >
                                    应用模板
                                  </Button>
                                </div>
                              )}
                            </div>
                          </div>
                        </Card>
                      ))}
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <Separator />

            {/* 交易设置 */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* 多头设置 */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-green-600">多头设置</h3>
                
                <FormField
                  control={form.control}
                  name="long_exchange"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>交易所</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="选择交易所" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {exchanges.map(exchange => (
                            <SelectItem key={exchange} value={exchange}>
                              {exchange.toUpperCase()}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="long_symbol"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>交易对</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="选择交易对" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {symbols.map(symbol => (
                            <SelectItem key={symbol} value={symbol}>
                              {symbol}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="long_is_spot"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center space-x-3 space-y-0">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel>现货交易</FormLabel>
                        <p className="text-sm text-muted-foreground">
                          取消勾选为合约交易
                        </p>
                      </div>
                    </FormItem>
                  )}
                />
              </div>

              {/* 空头设置 */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-red-600">空头设置</h3>
                
                <FormField
                  control={form.control}
                  name="short_exchange"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>交易所</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="选择交易所" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {exchanges.map(exchange => (
                            <SelectItem key={exchange} value={exchange}>
                              {exchange.toUpperCase()}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="short_symbol"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>交易对</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="选择交易对" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {symbols.map(symbol => (
                            <SelectItem key={symbol} value={symbol}>
                              {symbol}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="short_is_spot"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center space-x-3 space-y-0">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel>现货交易</FormLabel>
                        <p className="text-sm text-muted-foreground">
                          取消勾选为合约交易
                        </p>
                      </div>
                    </FormItem>
                  )}
                />
              </div>
            </div>

            <Separator />

            {/* 其他设置 */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormField
                control={form.control}
                name="amount_usdt"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>交易金额 (USDT)</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        step="0.01"
                        {...field}
                        onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="priority"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>优先级 (1-10)</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        min="1"
                        max="10"
                        {...field}
                        onChange={(e) => field.onChange(parseInt(e.target.value) || 1)}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* 错误提示 */}
            {submitStatus === 'error' && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>{errorMessage}</AlertDescription>
              </Alert>
            )}

            {/* 提交按钮 */}
            <div className="flex justify-end space-x-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => form.reset()}
              >
                重置
              </Button>
              <Button
                type="submit"
                disabled={loading || submitStatus === 'success'}
              >
                {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                {submitStatus === 'success' ? '已提交' : '创建套保订单'}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
};
