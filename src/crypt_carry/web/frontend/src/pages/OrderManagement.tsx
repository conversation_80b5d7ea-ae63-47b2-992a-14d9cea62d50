import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>Content,
  <PERSON><PERSON><PERSON>ist,
  TabsTrigger,
} from '@/components/ui/tabs';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { HedgingOrderForm } from '@/components/HedgingOrderForm';
import { SingleOrderForm } from '@/components/SingleOrderForm';
import { 
  Loader2, 
  RefreshCw, 
  X, 
  CheckCircle2, 
  Clock, 
  AlertCircle,
  TrendingUp,
  TrendingDown
} from 'lucide-react';

interface Order {
  order_id: string;
  type: 'hedging' | 'single';
  status: string;
  created_at: string;
  // 套保订单特有字段
  conditions?: Array<{
    type: string;
    trigger_value: number;
    enabled: boolean;
  }>;
  long_exchange?: string;
  long_symbol?: string;
  short_exchange?: string;
  short_symbol?: string;
  amount_usdt?: number;
  // 单独订单特有字段
  exchange?: string;
  symbol?: string;
  side?: string;
  amount?: number;
  price?: number;
}

export const OrderManagement: React.FC = () => {
  const [activeTab, setActiveTab] = useState('hedging');
  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(false);
  const [submitLoading, setSubmitLoading] = useState(false);
  const [error, setError] = useState<string>('');

  // 加载订单列表
  const loadOrders = async () => {
    try {
      setLoading(true);
      setError('');
      
      const response = await fetch('/api/hedging/orders/active');
      const data = await response.json();
      
      if (data.success) {
        setOrders(data.orders);
      } else {
        setError(data.error || '加载订单失败');
      }
    } catch (error) {
      setError('网络错误，请稍后重试');
      console.error('加载订单失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 初始加载
  useEffect(() => {
    loadOrders();
  }, []);

  // 提交套保订单
  const handleHedgingSubmit = async (data: any) => {
    try {
      setSubmitLoading(true);
      
      const response = await fetch('/api/hedging/orders/hedging', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });
      
      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || '创建套保订单失败');
      }
      
      // 重新加载订单列表
      await loadOrders();
      
    } catch (error) {
      throw error; // 让组件处理错误显示
    } finally {
      setSubmitLoading(false);
    }
  };

  // 提交单独订单
  const handleSingleSubmit = async (data: any) => {
    try {
      setSubmitLoading(true);
      
      const response = await fetch('/api/hedging/orders/single', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });
      
      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || '创建订单失败');
      }
      
      // 重新加载订单列表
      await loadOrders();
      
    } catch (error) {
      throw error; // 让组件处理错误显示
    } finally {
      setSubmitLoading(false);
    }
  };

  // 取消订单
  const handleCancelOrder = async (orderId: string) => {
    try {
      const response = await fetch(`/api/hedging/orders/${orderId}/cancel`, {
        method: 'POST',
      });
      
      const result = await response.json();
      
      if (result.success) {
        // 重新加载订单列表
        await loadOrders();
      } else {
        setError(result.error || '取消订单失败');
      }
    } catch (error) {
      setError('取消订单失败');
      console.error('取消订单失败:', error);
    }
  };

  // 获取状态显示
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge variant="default" className="bg-blue-500"><Clock className="w-3 h-3 mr-1" />活跃</Badge>;
      case 'filled':
        return <Badge variant="default" className="bg-green-500"><CheckCircle2 className="w-3 h-3 mr-1" />已成交</Badge>;
      case 'cancelled':
        return <Badge variant="secondary"><X className="w-3 h-3 mr-1" />已取消</Badge>;
      case 'failed':
        return <Badge variant="destructive"><AlertCircle className="w-3 h-3 mr-1" />失败</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  // 获取条件类型显示名称
  const getConditionTypeName = (type: string) => {
    switch (type) {
      case 'price_difference':
        return '价差';
      case 'funding_rate_diff':
        return '资金费率差异';
      case 'basis_rate':
        return '基差率';
      default:
        return type;
    }
  };

  // 渲染套保订单表格
  const renderHedgingOrders = () => {
    const hedgingOrders = orders.filter(order => order.type === 'hedging');
    
    return (
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>订单ID</TableHead>
            <TableHead>状态</TableHead>
            <TableHead>触发条件</TableHead>
            <TableHead>多头</TableHead>
            <TableHead>空头</TableHead>
            <TableHead>金额</TableHead>
            <TableHead>创建时间</TableHead>
            <TableHead>操作</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {hedgingOrders.map((order) => (
            <TableRow key={order.order_id}>
              <TableCell className="font-mono text-sm">
                {order.order_id.substring(0, 12)}...
              </TableCell>
              <TableCell>{getStatusBadge(order.status)}</TableCell>
              <TableCell>
                <div className="space-y-1">
                  {order.conditions?.filter(c => c.enabled).map((condition, index) => (
                    <Badge key={index} variant="outline" className="text-xs">
                      {getConditionTypeName(condition.type)}: {condition.trigger_value}
                    </Badge>
                  ))}
                </div>
              </TableCell>
              <TableCell>
                <div className="text-sm">
                  <div className="font-medium text-green-600">{order.long_exchange?.toUpperCase()}</div>
                  <div className="text-muted-foreground">{order.long_symbol}</div>
                </div>
              </TableCell>
              <TableCell>
                <div className="text-sm">
                  <div className="font-medium text-red-600">{order.short_exchange?.toUpperCase()}</div>
                  <div className="text-muted-foreground">{order.short_symbol}</div>
                </div>
              </TableCell>
              <TableCell>{order.amount_usdt} USDT</TableCell>
              <TableCell className="text-sm text-muted-foreground">
                {new Date(order.created_at).toLocaleString()}
              </TableCell>
              <TableCell>
                {order.status === 'active' && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleCancelOrder(order.order_id)}
                  >
                    <X className="w-3 h-3 mr-1" />
                    取消
                  </Button>
                )}
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    );
  };

  // 渲染单独订单表格
  const renderSingleOrders = () => {
    const singleOrders = orders.filter(order => order.type === 'single');
    
    return (
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>订单ID</TableHead>
            <TableHead>状态</TableHead>
            <TableHead>交易所</TableHead>
            <TableHead>交易对</TableHead>
            <TableHead>方向</TableHead>
            <TableHead>数量</TableHead>
            <TableHead>价格</TableHead>
            <TableHead>创建时间</TableHead>
            <TableHead>操作</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {singleOrders.map((order) => (
            <TableRow key={order.order_id}>
              <TableCell className="font-mono text-sm">
                {order.order_id.substring(0, 12)}...
              </TableCell>
              <TableCell>{getStatusBadge(order.status)}</TableCell>
              <TableCell className="font-medium">
                {order.exchange?.toUpperCase()}
              </TableCell>
              <TableCell>{order.symbol}</TableCell>
              <TableCell>
                <div className={`flex items-center gap-1 ${
                  order.side === 'buy' ? 'text-green-600' : 'text-red-600'
                }`}>
                  {order.side === 'buy' ? (
                    <TrendingUp className="w-3 h-3" />
                  ) : (
                    <TrendingDown className="w-3 h-3" />
                  )}
                  {order.side === 'buy' ? '买入' : '卖出'}
                </div>
              </TableCell>
              <TableCell>{order.amount}</TableCell>
              <TableCell>{order.price || '市价'}</TableCell>
              <TableCell className="text-sm text-muted-foreground">
                {new Date(order.created_at).toLocaleString()}
              </TableCell>
              <TableCell>
                {order.status === 'active' && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleCancelOrder(order.order_id)}
                  >
                    <X className="w-3 h-3 mr-1" />
                    取消
                  </Button>
                )}
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    );
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">订单管理</h1>
        <Button
          variant="outline"
          onClick={loadOrders}
          disabled={loading}
        >
          {loading ? (
            <Loader2 className="w-4 h-4 mr-2 animate-spin" />
          ) : (
            <RefreshCw className="w-4 h-4 mr-2" />
          )}
          刷新
        </Button>
      </div>

      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="hedging">套保下单</TabsTrigger>
          <TabsTrigger value="single">单独下单</TabsTrigger>
          <TabsTrigger value="orders">订单列表</TabsTrigger>
        </TabsList>

        <TabsContent value="hedging" className="space-y-6">
          <HedgingOrderForm 
            onSubmit={handleHedgingSubmit}
            loading={submitLoading}
          />
        </TabsContent>

        <TabsContent value="single" className="space-y-6">
          <SingleOrderForm 
            onSubmit={handleSingleSubmit}
            loading={submitLoading}
          />
        </TabsContent>

        <TabsContent value="orders" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">总订单数</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{orders.length}</div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">活跃订单</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-blue-600">
                  {orders.filter(o => o.status === 'active').length}
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">已成交订单</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">
                  {orders.filter(o => o.status === 'filled').length}
                </div>
              </CardContent>
            </Card>
          </div>

          <Tabs defaultValue="hedging-orders" className="space-y-4">
            <TabsList>
              <TabsTrigger value="hedging-orders">套保订单</TabsTrigger>
              <TabsTrigger value="single-orders">单独订单</TabsTrigger>
            </TabsList>

            <TabsContent value="hedging-orders">
              <Card>
                <CardHeader>
                  <CardTitle>套保订单列表</CardTitle>
                </CardHeader>
                <CardContent>
                  {loading ? (
                    <div className="flex justify-center py-8">
                      <Loader2 className="w-6 h-6 animate-spin" />
                    </div>
                  ) : (
                    renderHedgingOrders()
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="single-orders">
              <Card>
                <CardHeader>
                  <CardTitle>单独订单列表</CardTitle>
                </CardHeader>
                <CardContent>
                  {loading ? (
                    <div className="flex justify-center py-8">
                      <Loader2 className="w-6 h-6 animate-spin" />
                    </div>
                  ) : (
                    renderSingleOrders()
                  )}
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </TabsContent>
      </Tabs>
    </div>
  );
};
