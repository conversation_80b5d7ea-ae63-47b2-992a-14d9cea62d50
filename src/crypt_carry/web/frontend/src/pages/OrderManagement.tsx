import React, { useState, useEffect } from 'react';
import {
  Card,
  Tabs,
  Table,
  Button,
  Tag,
  Alert,
  Space,
  Statistic,
  Row,
  Col,
  message,
  Spin
} from 'antd';
import {
  ReloadOutlined,
  CloseOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined,
  RiseOutlined,
  FallOutlined
} from '@ant-design/icons';
import { HedgingOrderForm } from '../components/HedgingOrderForm';
import { SingleOrderForm } from '../components/SingleOrderForm';

interface Order {
  order_id: string;
  type: 'hedging' | 'single';
  status: string;
  created_at: string;
  // 套保订单特有字段
  conditions?: Array<{
    type: string;
    trigger_value: number;
    enabled: boolean;
  }>;
  long_exchange?: string;
  long_symbol?: string;
  short_exchange?: string;
  short_symbol?: string;
  amount_usdt?: number;
  // 单独订单特有字段
  exchange?: string;
  symbol?: string;
  side?: string;
  amount?: number;
  price?: number;
}

const { TabPane } = Tabs;

export const OrderManagement: React.FC = () => {
  const [activeTab, setActiveTab] = useState('hedging');
  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(false);
  const [submitLoading, setSubmitLoading] = useState(false);
  const [error, setError] = useState<string>('');

  // 加载订单列表
  const loadOrders = async () => {
    try {
      setLoading(true);
      setError('');

      const response = await fetch('/api/hedging/orders/active');
      const data = await response.json();

      if (data.success) {
        setOrders(data.orders);
      } else {
        setError(data.error || '加载订单失败');
      }
    } catch (error) {
      setError('网络错误，请稍后重试');
      console.error('加载订单失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 初始加载
  useEffect(() => {
    loadOrders();
  }, []);

  // 提交套保订单
  const handleHedgingSubmit = async (data: any) => {
    try {
      setSubmitLoading(true);

      const response = await fetch('/api/hedging/orders/hedging', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error || '创建套保订单失败');
      }

      // 重新加载订单列表
      await loadOrders();

    } catch (error) {
      throw error; // 让组件处理错误显示
    } finally {
      setSubmitLoading(false);
    }
  };

  // 提交单独订单
  const handleSingleSubmit = async (data: any) => {
    try {
      setSubmitLoading(true);

      const response = await fetch('/api/hedging/orders/single', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error || '创建订单失败');
      }

      // 重新加载订单列表
      await loadOrders();

    } catch (error) {
      throw error; // 让组件处理错误显示
    } finally {
      setSubmitLoading(false);
    }
  };

  // 取消订单
  const handleCancelOrder = async (orderId: string) => {
    try {
      const response = await fetch(`/api/hedging/orders/${orderId}/cancel`, {
        method: 'POST',
      });

      const result = await response.json();

      if (result.success) {
        // 重新加载订单列表
        await loadOrders();
      } else {
        setError(result.error || '取消订单失败');
      }
    } catch (error) {
      setError('取消订单失败');
      console.error('取消订单失败:', error);
    }
  };

  // 获取状态显示
  const getStatusTag = (status: string) => {
    switch (status) {
      case 'active':
        return <Tag icon={<ClockCircleOutlined />} color="blue">活跃</Tag>;
      case 'filled':
        return <Tag icon={<CheckCircleOutlined />} color="green">已成交</Tag>;
      case 'cancelled':
        return <Tag icon={<CloseOutlined />} color="default">已取消</Tag>;
      case 'failed':
        return <Tag icon={<ExclamationCircleOutlined />} color="red">失败</Tag>;
      default:
        return <Tag>{status}</Tag>;
    }
  };

  // 获取条件类型显示名称
  const getConditionTypeName = (type: string) => {
    switch (type) {
      case 'price_difference':
        return '价差';
      case 'funding_rate_diff':
        return '资金费率差异';
      case 'basis_rate':
        return '基差率';
      default:
        return type;
    }
  };

  // 套保订单表格列定义
  const hedgingColumns = [
    {
      title: '订单ID',
      dataIndex: 'order_id',
      key: 'order_id',
      render: (text: string) => (
        <span style={{ fontFamily: 'monospace', fontSize: '12px' }}>
          {text.substring(0, 12)}...
        </span>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => getStatusTag(status),
    },
    {
      title: '触发条件',
      dataIndex: 'conditions',
      key: 'conditions',
      render: (conditions: any[]) => (
        <Space direction="vertical" size="small">
          {conditions?.filter(c => c.enabled).map((condition, index) => (
            <Tag key={index}>
              {getConditionTypeName(condition.type)}: {condition.trigger_value}
            </Tag>
          ))}
        </Space>
      ),
    },
    {
      title: '多头',
      key: 'long',
      render: (record: Order) => (
        <div>
          <div style={{ fontWeight: 'bold', color: '#52c41a' }}>
            {record.long_exchange?.toUpperCase()}
          </div>
          <div style={{ color: '#8c8c8c', fontSize: '12px' }}>
            {record.long_symbol}
          </div>
        </div>
      ),
    },
    {
      title: '空头',
      key: 'short',
      render: (record: Order) => (
        <div>
          <div style={{ fontWeight: 'bold', color: '#ff4d4f' }}>
            {record.short_exchange?.toUpperCase()}
          </div>
          <div style={{ color: '#8c8c8c', fontSize: '12px' }}>
            {record.short_symbol}
          </div>
        </div>
      ),
    },
    {
      title: '金额',
      dataIndex: 'amount_usdt',
      key: 'amount_usdt',
      render: (amount: number) => `${amount} USDT`,
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (time: string) => new Date(time).toLocaleString(),
    },
    {
      title: '操作',
      key: 'action',
      render: (record: Order) => (
        record.status === 'active' && (
          <Button
            type="link"
            danger
            size="small"
            icon={<CloseOutlined />}
            onClick={() => handleCancelOrder(record.order_id)}
          >
            取消
          </Button>
        )
      ),
    },
  ];

  // 单独订单表格列定义
  const singleColumns = [
    {
      title: '订单ID',
      dataIndex: 'order_id',
      key: 'order_id',
      render: (text: string) => (
        <span style={{ fontFamily: 'monospace', fontSize: '12px' }}>
          {text.substring(0, 12)}...
        </span>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => getStatusTag(status),
    },
    {
      title: '交易所',
      dataIndex: 'exchange',
      key: 'exchange',
      render: (exchange: string) => (
        <span style={{ fontWeight: 'bold' }}>
          {exchange?.toUpperCase()}
        </span>
      ),
    },
    {
      title: '交易对',
      dataIndex: 'symbol',
      key: 'symbol',
    },
    {
      title: '方向',
      dataIndex: 'side',
      key: 'side',
      render: (side: string) => (
        <Space>
          {side === 'buy' ? (
            <RiseOutlined style={{ color: '#52c41a' }} />
          ) : (
            <FallOutlined style={{ color: '#ff4d4f' }} />
          )}
          <span style={{ color: side === 'buy' ? '#52c41a' : '#ff4d4f' }}>
            {side === 'buy' ? '买入' : '卖出'}
          </span>
        </Space>
      ),
    },
    {
      title: '数量',
      dataIndex: 'amount',
      key: 'amount',
    },
    {
      title: '价格',
      dataIndex: 'price',
      key: 'price',
      render: (price: number) => price || '市价',
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (time: string) => new Date(time).toLocaleString(),
    },
    {
      title: '操作',
      key: 'action',
      render: (record: Order) => (
        record.status === 'active' && (
          <Button
            type="link"
            danger
            size="small"
            icon={<CloseOutlined />}
            onClick={() => handleCancelOrder(record.order_id)}
          >
            取消
          </Button>
        )
      ),
    },
  ];

  const hedgingOrders = orders.filter(order => order.type === 'hedging');
  const singleOrders = orders.filter(order => order.type === 'single');

  return (
    <div style={{ padding: '24px' }}>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '24px' }}>
        <h1 style={{ fontSize: '24px', fontWeight: 'bold', margin: 0 }}>订单管理</h1>
        <Button
          type="default"
          icon={loading ? <Spin size="small" /> : <ReloadOutlined />}
          onClick={loadOrders}
          loading={loading}
        >
          刷新
        </Button>
      </div>

      {error && (
        <Alert
          message="错误"
          description={error}
          type="error"
          showIcon
          style={{ marginBottom: '24px' }}
        />
      )}

      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab="套保下单" key="hedging">
          <HedgingOrderForm
            onSubmit={handleHedgingSubmit}
            loading={submitLoading}
          />
        </TabPane>

        <TabPane tab="单独下单" key="single">
          <SingleOrderForm
            onSubmit={handleSingleSubmit}
            loading={submitLoading}
          />
        </TabPane>

        <TabPane tab="订单列表" key="orders">
          <Row gutter={16} style={{ marginBottom: '24px' }}>
            <Col span={8}>
              <Card>
                <Statistic
                  title="总订单数"
                  value={orders.length}
                  valueStyle={{ color: '#1890ff' }}
                />
              </Card>
            </Col>
            <Col span={8}>
              <Card>
                <Statistic
                  title="活跃订单"
                  value={orders.filter(o => o.status === 'active').length}
                  valueStyle={{ color: '#52c41a' }}
                />
              </Card>
            </Col>
            <Col span={8}>
              <Card>
                <Statistic
                  title="已成交订单"
                  value={orders.filter(o => o.status === 'filled').length}
                  valueStyle={{ color: '#faad14' }}
                />
              </Card>
            </Col>
          </Row>

          <Tabs defaultActiveKey="hedging-orders">
            <TabPane tab="套保订单" key="hedging-orders">
              <Card title="套保订单列表">
                <Table
                  columns={hedgingColumns}
                  dataSource={hedgingOrders}
                  rowKey="order_id"
                  loading={loading}
                  pagination={{ pageSize: 10 }}
                />
              </Card>
            </TabPane>

            <TabPane tab="单独订单" key="single-orders">
              <Card title="单独订单列表">
                <Table
                  columns={singleColumns}
                  dataSource={singleOrders}
                  rowKey="order_id"
                  loading={loading}
                  pagination={{ pageSize: 10 }}
                />
              </Card>
            </TabPane>
          </Tabs>
        </TabPane>
      </Tabs>
    </div>
  );
};
