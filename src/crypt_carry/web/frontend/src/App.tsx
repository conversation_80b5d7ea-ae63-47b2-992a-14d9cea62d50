import React from 'react';
import {BrowserRouter as Router, Routes, Route} from 'react-router-dom';
import {Layout} from 'antd';
import AppHeader from './components/layout/AppHeader';
import AppFooter from './components/layout/AppFooter';
import HomePage from './pages/HomePage';
import HedgingStrategyPage from './pages/HedgingStrategyPage';
import PositionsPage from './pages/PositionsPage';
import './App.css';

const {Content} = Layout;

/**
 * 应用主组件
 * 定义整体布局和路由
 */
const App: React.FC = () => {
    return (
        <Router>
            <Layout className="app-layout">
                <AppHeader/>
                <Content className="site-content">
                    <Routes>
                        <Route path="/" element={<HomePage/>}/>
                        <Route path="/hedging" element={<HedgingStrategyPage/>}/>
                        <Route path="/positions" element={<PositionsPage/>}/>
                    </Routes>
                </Content>
                <AppFooter/>
            </Layout>
        </Router>
    );
};

export default App;
