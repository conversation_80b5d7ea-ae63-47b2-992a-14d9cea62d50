#!/bin/bash

# Crypt Carry Web Services 启动脚本
# 用于同时启动前后端服务

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查命令是否存在
check_command() {
    if ! command -v $1 &> /dev/null; then
        log_error "$1 未安装，请先安装 $1"
        exit 1
    fi
}

# 检查端口是否被占用
check_port() {
    local port=$1
    local service=$2

    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        log_warning "端口 $port 已被占用 ($service)"
        read -p "是否要杀死占用进程? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            local pid=$(lsof -Pi :$port -sTCP:LISTEN -t)
            kill -9 $pid 2>/dev/null || true
            log_success "已杀死进程 $pid"
        else
            log_error "端口冲突，退出启动"
            exit 1
        fi
    fi
}

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
WEB_DIR="$SCRIPT_DIR"
FRONTEND_DIR="$WEB_DIR/frontend"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../../.." && pwd)"

log_info "Crypt Carry Web Services 启动脚本"
log_info "项目根目录: $PROJECT_ROOT"
log_info "Web目录: $WEB_DIR"
log_info "前端目录: $FRONTEND_DIR"

# 检查必要的命令
log_info "检查依赖..."
check_command "python3"
check_command "node"
check_command "yarn"

# 检查端口
log_info "检查端口占用..."
check_port 5000 "后端服务"
check_port 5173 "前端服务"

# 检查Python环境
log_info "检查Python环境..."

# 检查是否在conda环境中
if [ ! -z "$CONDA_DEFAULT_ENV" ]; then
    log_success "检测到conda环境: $CONDA_DEFAULT_ENV"
    PYTHON_ENV="conda"
elif [ ! -z "$VIRTUAL_ENV" ]; then
    log_success "检测到虚拟环境: $VIRTUAL_ENV"
    PYTHON_ENV="venv"
else
    log_warning "未检测到Python虚拟环境"

    # 检查是否存在项目虚拟环境
    VENV_PATH="$PROJECT_ROOT/venv"
    if [ -d "$VENV_PATH" ]; then
        log_info "激活项目虚拟环境..."
        source "$VENV_PATH/bin/activate"
        PYTHON_ENV="venv"
    else
        log_warning "建议使用conda或创建虚拟环境"
        read -p "是否创建虚拟环境? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            log_info "创建虚拟环境..."
            cd "$PROJECT_ROOT"
            python3 -m venv venv
            source "$VENV_PATH/bin/activate"
            log_success "虚拟环境创建并激活完成"
            PYTHON_ENV="venv"
        else
            log_info "使用系统Python环境"
            PYTHON_ENV="system"
        fi
    fi
fi

# 安装后端依赖
log_info "检查后端依赖..."
cd "$WEB_DIR"
if [ ! -f "requirements.txt" ]; then
    log_error "requirements.txt 不存在"
    exit 1
fi

# 检查是否需要安装依赖
pip list | grep -q flask || {
    log_info "安装后端依赖..."
    pip install -r requirements.txt
    log_success "后端依赖安装完成"
}

# 检查前端依赖
log_info "检查前端依赖..."
cd "$FRONTEND_DIR"
if [ ! -f "package.json" ]; then
    log_error "package.json 不存在"
    exit 1
fi

if [ ! -d "node_modules" ]; then
    log_info "安装前端依赖..."
    yarn install
    log_success "前端依赖安装完成"
fi

# 创建日志目录
mkdir -p "$WEB_DIR/logs"

# 启动服务的函数
start_backend() {
    log_info "启动后端服务..."
    cd "$WEB_DIR"

    # 设置环境变量
    export FLASK_APP=app.py
    export FLASK_ENV=development
    export FLASK_DEBUG=1
    export PYTHONPATH="$PROJECT_ROOT/src:$PYTHONPATH"

    # 启动Flask应用
    python app.py > logs/backend.log 2>&1 &
    BACKEND_PID=$!

    # 等待服务启动
    sleep 3

    # 检查服务是否启动成功
    if curl -s http://localhost:5000/health > /dev/null 2>&1; then
        log_success "后端服务启动成功 (PID: $BACKEND_PID)"
        echo $BACKEND_PID > logs/backend.pid
    else
        log_error "后端服务启动失败"
        kill $BACKEND_PID 2>/dev/null || true
        exit 1
    fi
}

start_frontend() {
    log_info "启动前端服务..."
    cd "$FRONTEND_DIR"

    # 启动Vite开发服务器
    yarn dev > ../logs/frontend.log 2>&1 &
    FRONTEND_PID=$!

    # 等待服务启动
    sleep 5

    # 检查服务是否启动成功
    if curl -s http://localhost:5173 > /dev/null 2>&1; then
        log_success "前端服务启动成功 (PID: $FRONTEND_PID)"
        echo $FRONTEND_PID > ../logs/frontend.pid
    else
        log_error "前端服务启动失败"
        kill $FRONTEND_PID 2>/dev/null || true
        exit 1
    fi
}

# 停止服务的函数
stop_services() {
    log_info "正在停止服务..."

    # 停止后端服务
    if [ -f "$WEB_DIR/logs/backend.pid" ]; then
        BACKEND_PID=$(cat "$WEB_DIR/logs/backend.pid")
        kill $BACKEND_PID 2>/dev/null || true
        rm -f "$WEB_DIR/logs/backend.pid"
        log_success "后端服务已停止"
    fi

    # 停止前端服务
    if [ -f "$WEB_DIR/logs/frontend.pid" ]; then
        FRONTEND_PID=$(cat "$WEB_DIR/logs/frontend.pid")
        kill $FRONTEND_PID 2>/dev/null || true
        rm -f "$WEB_DIR/logs/frontend.pid"
        log_success "前端服务已停止"
    fi

    # 杀死可能残留的进程
    pkill -f "python.*app.py" 2>/dev/null || true
    pkill -f "node.*vite" 2>/dev/null || true
}

# 信号处理
trap stop_services EXIT INT TERM

# 解析命令行参数
case "${1:-start}" in
    "start")
        log_info "启动所有服务..."
        start_backend
        start_frontend

        log_success "所有服务启动完成!"
        log_info "后端服务: http://localhost:5000"
        log_info "前端服务: http://localhost:5173"
        log_info "API文档: http://localhost:5000/api/docs"
        log_info ""
        log_info "按 Ctrl+C 停止所有服务"

        # 等待用户中断
        while true; do
            sleep 1
        done
        ;;

    "stop")
        stop_services
        ;;

    "restart")
        stop_services
        sleep 2
        start_backend
        start_frontend
        log_success "服务重启完成!"
        ;;

    "status")
        log_info "检查服务状态..."

        # 检查后端
        if curl -s http://localhost:5000/health > /dev/null 2>&1; then
            log_success "后端服务运行中 (http://localhost:5000)"
        else
            log_error "后端服务未运行"
        fi

        # 检查前端
        if curl -s http://localhost:5173 > /dev/null 2>&1; then
            log_success "前端服务运行中 (http://localhost:5173)"
        else
            log_error "前端服务未运行"
        fi
        ;;

    "logs")
        log_info "查看服务日志..."
        echo "=== 后端日志 ==="
        tail -n 20 "$WEB_DIR/logs/backend.log" 2>/dev/null || echo "无后端日志"
        echo ""
        echo "=== 前端日志 ==="
        tail -n 20 "$WEB_DIR/logs/frontend.log" 2>/dev/null || echo "无前端日志"
        ;;

    "help"|"-h"|"--help")
        echo "用法: $0 [命令]"
        echo ""
        echo "命令:"
        echo "  start    启动所有服务 (默认)"
        echo "  stop     停止所有服务"
        echo "  restart  重启所有服务"
        echo "  status   检查服务状态"
        echo "  logs     查看服务日志"
        echo "  help     显示此帮助信息"
        echo ""
        echo "示例:"
        echo "  $0 start    # 启动服务"
        echo "  $0 status   # 检查状态"
        echo "  $0 logs     # 查看日志"
        ;;

    *)
        log_error "未知命令: $1"
        log_info "使用 '$0 help' 查看帮助"
        exit 1
        ;;
esac
