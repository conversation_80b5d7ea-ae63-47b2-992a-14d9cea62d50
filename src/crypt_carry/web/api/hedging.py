"""
套保策略API蓝图
提供套保策略的创建、查询和执行功能
"""
import json
import logging
import os
from datetime import datetime

from flask import Blueprint, jsonify, request

# 创建蓝图
hedging_blueprint = Blueprint('hedging', __name__)

# 配置日志
logger = logging.getLogger(__name__)

# 本地存储路径（使用JSON文件代替数据库）
try:
    from crypt_carry.utils.paths import get_web_data_dir

    STORAGE_PATH = str(get_web_data_dir())
    HEDGING_CONFIG_FILE = str(get_web_data_dir() / 'hedging_config.json')
except ImportError:
    # 如果无法导入paths模块，使用原来的方法作为备选
    STORAGE_PATH = os.path.join(os.path.dirname(__file__), '..', 'data')
    HEDGING_CONFIG_FILE = os.path.join(STORAGE_PATH, 'hedging_config.json')

# 确保存储目录存在
os.makedirs(STORAGE_PATH, exist_ok=True)

# 内存中的套保配置
_hedging_config = {
    'strategies': [],
    'active_positions': []
}


def _save_config():
    """保存套保配置到本地文件"""
    try:
        with open(HEDGING_CONFIG_FILE, 'w', encoding='utf-8') as f:
            json.dump(_hedging_config, f, ensure_ascii=False, indent=2)
        return True
    except Exception as e:
        logger.error(f"保存套保配置失败: {str(e)}")
        return False


def _load_config():
    """从本地文件加载套保配置"""
    global _hedging_config
    if os.path.exists(HEDGING_CONFIG_FILE):
        try:
            with open(HEDGING_CONFIG_FILE, 'r', encoding='utf-8') as f:
                _hedging_config = json.load(f)
        except Exception as e:
            logger.error(f"加载套保配置失败: {str(e)}")


# 初始加载配置
_load_config()


@hedging_blueprint.route('/strategies', methods=['GET'])
def get_strategies():
    """获取所有套保策略"""
    return jsonify(_hedging_config['strategies'])


@hedging_blueprint.route('/strategies', methods=['POST'])
def create_strategy():
    """创建新的套保策略"""
    data = request.json

    # 基本验证
    if not data or 'name' not in data:
        return jsonify({'error': '必须提供策略名称'}), 400

    # 生成策略ID
    strategy_id = len(_hedging_config['strategies']) + 1

    # 创建策略对象
    strategy = {
        'id': strategy_id,
        'name': data.get('name'),
        'description': data.get('description', ''),
        'is_active': data.get('is_active', True),
        'created_at': datetime.now().isoformat(),
        'conditions': []
    }

    # 添加条件（如果提供）
    if 'conditions' in data and isinstance(data['conditions'], list):
        for condition in data['conditions']:
            if _validate_condition(condition):
                strategy['conditions'].append(condition)

    # 保存策略
    _hedging_config['strategies'].append(strategy)
    _save_config()

    return jsonify(strategy), 201


@hedging_blueprint.route('/strategies/<int:strategy_id>', methods=['GET'])
def get_strategy(strategy_id):
    """获取指定ID的套保策略"""
    for strategy in _hedging_config['strategies']:
        if strategy['id'] == strategy_id:
            return jsonify(strategy)

    return jsonify({'error': '策略不存在'}), 404


@hedging_blueprint.route('/strategies/<int:strategy_id>', methods=['PUT'])
def update_strategy(strategy_id):
    """更新套保策略"""
    data = request.json

    for i, strategy in enumerate(_hedging_config['strategies']):
        if strategy['id'] == strategy_id:
            # 更新可修改的字段
            if 'name' in data:
                strategy['name'] = data['name']
            if 'description' in data:
                strategy['description'] = data['description']
            if 'is_active' in data:
                strategy['is_active'] = data['is_active']

            # 更新条件（如果提供）
            if 'conditions' in data and isinstance(data['conditions'], list):
                strategy['conditions'] = []
                for condition in data['conditions']:
                    if _validate_condition(condition):
                        strategy['conditions'].append(condition)

            _save_config()
            return jsonify(strategy)

    return jsonify({'error': '策略不存在'}), 404


@hedging_blueprint.route('/strategies/<int:strategy_id>', methods=['DELETE'])
def delete_strategy(strategy_id):
    """删除套保策略"""
    for i, strategy in enumerate(_hedging_config['strategies']):
        if strategy['id'] == strategy_id:
            del _hedging_config['strategies'][i]
            _save_config()
            return jsonify({'message': '策略已删除'})

    return jsonify({'error': '策略不存在'}), 404


@hedging_blueprint.route('/strategies/<int:strategy_id>/execute', methods=['POST'])
def execute_strategy(strategy_id):
    """执行套保策略"""
    # 查找策略
    strategy = None
    for s in _hedging_config['strategies']:
        if s['id'] == strategy_id:
            strategy = s
            break

    if not strategy:
        return jsonify({'error': '策略不存在'}), 404

    if not strategy.get('is_active', False):
        return jsonify({'error': '策略未激活，无法执行'}), 400

    # TODO: 实际执行套保策略的代码
    # 这里需要调用您现有的套保功能代码

    # 模拟返回执行结果
    result = {
        'success': True,
        'strategy_id': strategy_id,
        'message': f'策略 {strategy["name"]} 执行成功',
        'execution_time': datetime.now().isoformat(),
    }

    return jsonify(result)


@hedging_blueprint.route('/positions', methods=['GET'])
def get_positions():
    """获取所有活跃的套保持仓"""
    return jsonify(_hedging_config['active_positions'])


@hedging_blueprint.route('/positions/<int:position_id>/close', methods=['POST'])
def close_position(position_id):
    """关闭特定的套保持仓"""
    for i, position in enumerate(_hedging_config['active_positions']):
        if position['id'] == position_id:
            # TODO: 实际平仓代码
            # 这里需要调用您现有的平仓功能代码

            # 从活跃持仓中移除
            position = _hedging_config['active_positions'].pop(i)
            _save_config()

            return jsonify({
                'success': True,
                'message': f'持仓 {position_id} 已平仓',
                'position': position
            })

    return jsonify({'error': '持仓不存在'}), 404


def _validate_condition(condition):
    """验证套保条件是否有效"""
    required_fields = [
        'long_exchange', 'long_symbol', 'long_amount',
        'short_exchange', 'short_symbol', 'short_amount'
    ]

    for field in required_fields:
        if field not in condition:
            return False

    # 验证交易所
    supported_exchanges = ['binance', 'okx']
    if condition['long_exchange'].lower() not in supported_exchanges or \
            condition['short_exchange'].lower() not in supported_exchanges:
        return False

    return True
