"""
套保策略API路由模块
定义与套保策略和持仓管理相关的API端点
"""
import json
import os
import asyncio
import functools
from datetime import datetime
from typing import Dict, List, Optional

from flask import jsonify, request
from flask_restx import Resource

from . import hedging_blueprint
from ..docs import (
    api, hedging_ns,
    hedging_order_request, hedging_order_response,
    single_order_request, single_order_response,
    api_response_model, strategy_info_model
)


def async_to_sync(f):
    """异步转同步装饰器"""
    @functools.wraps(f)
    def wrapper(*args, **kwargs):
        return asyncio.run(f(*args, **kwargs))
    return wrapper

# 内存中的配置数据，实际应用中应该使用数据库
_hedging_config = {
    'strategies': [],  # 套保策略列表
    'active_positions': [],  # 活跃持仓列表
    'next_strategy_id': 1,
    'next_position_id': 1,
    'next_condition_id': 1
}

# 配置文件路径
try:
    from crypt_carry.utils.paths import get_web_data_dir

    _CONFIG_FILE = str(get_web_data_dir() / 'hedging_config.json')
except ImportError:
    # 如果无法导入paths模块，使用原来的方法作为备选
    _CONFIG_FILE = os.path.join(os.path.dirname(__file__), '../../data/hedging_config.json')


def _load_config():
    """从文件加载配置"""
    global _hedging_config
    os.makedirs(os.path.dirname(_CONFIG_FILE), exist_ok=True)
    try:
        if os.path.exists(_CONFIG_FILE):
            with open(_CONFIG_FILE, 'r', encoding='utf-8') as f:
                _hedging_config = json.load(f)
    except Exception as e:
        print(f"加载配置失败: {e}")


def _save_config():
    """保存配置到文件"""
    os.makedirs(os.path.dirname(_CONFIG_FILE), exist_ok=True)
    try:
        with open(_CONFIG_FILE, 'w', encoding='utf-8') as f:
            json.dump(_hedging_config, f, ensure_ascii=False, indent=2)
    except Exception as e:
        print(f"保存配置失败: {e}")


# 初始化加载配置
_load_config()

# 全局套保策略管理器实例
_global_hedging_manager = None


def _get_hedging_manager():
    """获取全局套保策略管理器实例"""
    global _global_hedging_manager
    if _global_hedging_manager is None:
        from crypt_carry.strategies.hedging.hedging_manager import HedgingStrategyManager
        _global_hedging_manager = HedgingStrategyManager()
    return _global_hedging_manager


def _get_order_manager():
    """获取全局订单管理器实例"""
    from crypt_carry.core.exchange.account.order_manager import OrderManager
    return OrderManager.get_instance()


@hedging_blueprint.route('/strategies', methods=['GET'])
@hedging_ns.doc('get_strategies')
@hedging_ns.marshal_list_with(strategy_info_model)
def get_strategies():
    """
    获取所有套保策略

    返回:
        JSON格式的策略列表
    """
    return jsonify(_hedging_config['strategies'])


@hedging_blueprint.route('/strategies', methods=['POST'])
def create_strategy():
    """
    创建新的套保策略

    请求体:
        JSON格式的策略数据

    返回:
        JSON格式的创建结果，包含新策略的ID
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': '无效的请求数据'}), 400

        # 验证必要字段
        required_fields = ['name', 'is_active']
        for field in required_fields:
            if field not in data:
                return jsonify({'error': f'缺少必要字段: {field}'}), 400

        # 创建新策略
        strategy_id = _hedging_config['next_strategy_id']
        _hedging_config['next_strategy_id'] += 1

        new_strategy = {
            'id': strategy_id,
            'name': data['name'],
            'description': data.get('description', ''),
            'is_active': data['is_active'],
            'created_at': datetime.now().isoformat(),
            'updated_at': datetime.now().isoformat(),
            'check_interval': data.get('check_interval', 300),
            'auto_close': data.get('auto_close', True),
            'max_open_positions': data.get('max_open_positions', 5),
            'conditions': []
        }

        _hedging_config['strategies'].append(new_strategy)
        _save_config()

        return jsonify(new_strategy), 201

    except Exception as e:
        return jsonify({'error': f'创建策略失败: {str(e)}'}), 500


@hedging_blueprint.route('/strategies/<int:strategy_id>', methods=['GET'])
def get_strategy(strategy_id):
    """
    获取特定的套保策略

    参数:
        strategy_id: 策略ID

    返回:
        JSON格式的策略数据
    """
    for strategy in _hedging_config['strategies']:
        if strategy['id'] == strategy_id:
            return jsonify(strategy)

    return jsonify({'error': f'找不到策略 ID: {strategy_id}'}), 404


@hedging_blueprint.route('/strategies/<int:strategy_id>', methods=['PUT'])
def update_strategy(strategy_id):
    """
    更新特定的套保策略

    参数:
        strategy_id: 策略ID

    请求体:
        JSON格式的策略更新数据

    返回:
        JSON格式的更新结果
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': '无效的请求数据'}), 400

        for i, strategy in enumerate(_hedging_config['strategies']):
            if strategy['id'] == strategy_id:
                # 更新策略字段
                for key, value in data.items():
                    if key != 'id' and key != 'created_at' and key != 'conditions':
                        strategy[key] = value

                strategy['updated_at'] = datetime.now().isoformat()
                _save_config()

                return jsonify(strategy)

        return jsonify({'error': f'找不到策略 ID: {strategy_id}'}), 404

    except Exception as e:
        return jsonify({'error': f'更新策略失败: {str(e)}'}), 500


@hedging_blueprint.route('/strategies/<int:strategy_id>', methods=['DELETE'])
def delete_strategy(strategy_id):
    """
    删除特定的套保策略

    参数:
        strategy_id: 策略ID

    返回:
        JSON格式的删除结果
    """
    for i, strategy in enumerate(_hedging_config['strategies']):
        if strategy['id'] == strategy_id:
            del _hedging_config['strategies'][i]
            _save_config()

            return jsonify({'success': True, 'message': f'策略 ID: {strategy_id} 已删除'})

    return jsonify({'error': f'找不到策略 ID: {strategy_id}'}), 404


@hedging_blueprint.route('/strategies/<int:strategy_id>/conditions', methods=['GET'])
def get_conditions(strategy_id):
    """
    获取特定策略的所有条件

    参数:
        strategy_id: 策略ID

    返回:
        JSON格式的条件列表
    """
    for strategy in _hedging_config['strategies']:
        if strategy['id'] == strategy_id:
            return jsonify(strategy['conditions'])

    return jsonify({'error': f'找不到策略 ID: {strategy_id}'}), 404


@hedging_blueprint.route('/strategies/<int:strategy_id>/conditions', methods=['POST'])
def create_condition(strategy_id):
    """
    为特定策略创建新条件

    参数:
        strategy_id: 策略ID

    请求体:
        JSON格式的条件数据

    返回:
        JSON格式的创建结果，包含新条件的ID
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': '无效的请求数据'}), 400

        # 验证必要字段
        required_fields = [
            'condition_type', 'long_exchange', 'long_symbol', 'long_amount',
            'short_exchange', 'short_symbol', 'short_amount', 'trigger_value',
            'comparison_operator'
        ]
        for field in required_fields:
            if field not in data:
                return jsonify({'error': f'缺少必要字段: {field}'}), 400

        for i, strategy in enumerate(_hedging_config['strategies']):
            if strategy['id'] == strategy_id:
                # 创建新条件
                condition_id = _hedging_config['next_condition_id']
                _hedging_config['next_condition_id'] += 1

                new_condition = {
                    'id': condition_id,
                    'condition_type': data['condition_type'],
                    'long_exchange': data['long_exchange'],
                    'long_symbol': data['long_symbol'],
                    'long_amount': data['long_amount'],
                    'long_is_spot': data.get('long_is_spot', True),
                    'short_exchange': data['short_exchange'],
                    'short_symbol': data['short_symbol'],
                    'short_amount': data['short_amount'],
                    'short_is_spot': data.get('short_is_spot', False),
                    'trigger_value': data['trigger_value'],
                    'comparison_operator': data['comparison_operator'],
                    'is_active': data.get('is_active', True),
                    'priority': data.get('priority', 1)
                }

                strategy['conditions'].append(new_condition)
                _save_config()

                return jsonify(new_condition), 201

        return jsonify({'error': f'找不到策略 ID: {strategy_id}'}), 404

    except Exception as e:
        return jsonify({'error': f'创建条件失败: {str(e)}'}), 500


@hedging_blueprint.route('/strategies/<int:strategy_id>/conditions/<int:condition_id>', methods=['PUT'])
def update_condition(strategy_id, condition_id):
    """
    更新特定条件

    参数:
        strategy_id: 策略ID
        condition_id: 条件ID

    请求体:
        JSON格式的条件更新数据

    返回:
        JSON格式的更新结果
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': '无效的请求数据'}), 400

        for strategy in _hedging_config['strategies']:
            if strategy['id'] == strategy_id:
                for i, condition in enumerate(strategy['conditions']):
                    if condition['id'] == condition_id:
                        # 更新条件字段
                        for key, value in data.items():
                            if key != 'id':
                                condition[key] = value

                        _save_config()

                        return jsonify(condition)

                return jsonify({'error': f'找不到条件 ID: {condition_id}'}), 404

        return jsonify({'error': f'找不到策略 ID: {strategy_id}'}), 404

    except Exception as e:
        return jsonify({'error': f'更新条件失败: {str(e)}'}), 500


@hedging_blueprint.route('/strategies/<int:strategy_id>/conditions/<int:condition_id>', methods=['DELETE'])
def delete_condition(strategy_id, condition_id):
    """
    删除特定条件

    参数:
        strategy_id: 策略ID
        condition_id: 条件ID

    返回:
        JSON格式的删除结果
    """
    for strategy in _hedging_config['strategies']:
        if strategy['id'] == strategy_id:
            for i, condition in enumerate(strategy['conditions']):
                if condition['id'] == condition_id:
                    del strategy['conditions'][i]
                    _save_config()

                    return jsonify({'success': True, 'message': f'条件 ID: {condition_id} 已删除'})

            return jsonify({'error': f'找不到条件 ID: {condition_id}'}), 404

    return jsonify({'error': f'找不到策略 ID: {strategy_id}'}), 404


@hedging_blueprint.route('/strategies/<int:strategy_id>/execute', methods=['POST'])
def execute_strategy(strategy_id):
    """
    手动执行特定策略

    参数:
        strategy_id: 策略ID

    返回:
        JSON格式的执行结果
    """
    for strategy in _hedging_config['strategies']:
        if strategy['id'] == strategy_id:
            if not strategy['is_active']:
                return jsonify({'error': '策略未激活，无法执行'}), 400

            # 集成新的套保策略管理器
            try:
                from crypt_carry.strategies.hedging import HedgingStrategyManager

                # 这里需要获取全局的套保策略管理器实例
                # 暂时返回模拟结果，实际使用时需要集成真实的管理器

                result = {
                    'success': True,
                    'strategy_id': strategy_id,
                    'message': f'策略 {strategy["name"]} 执行成功',
                    'execution_time': datetime.now().isoformat(),
                    'note': '已集成新的套保策略框架，等待实际执行逻辑'
                }
            except Exception as e:
                result = {
                    'success': False,
                    'strategy_id': strategy_id,
                    'message': f'策略执行失败: {str(e)}',
                    'execution_time': datetime.now().isoformat(),
                }

            return jsonify(result)

    return jsonify({'error': f'找不到策略 ID: {strategy_id}'}), 404


@hedging_blueprint.route('/positions', methods=['GET'])
def get_positions():
    """
    获取所有活跃的套保持仓

    返回:
        JSON格式的持仓列表
    """
    return jsonify(_hedging_config['active_positions'])


@hedging_blueprint.route('/positions/<int:position_id>/close', methods=['POST'])
def close_position(position_id):
    """
    关闭特定的套保持仓

    参数:
        position_id: 持仓ID

    返回:
        JSON格式的平仓结果
    """
    for i, position in enumerate(_hedging_config['active_positions']):
        if position['id'] == position_id:
            # TODO: 实际平仓代码
            # 这里需要调用您现有的平仓功能代码

            # 从活跃持仓中移除
            position = _hedging_config['active_positions'].pop(i)
            position['status'] = 'closed'
            position['closed_at'] = datetime.now().isoformat()
            _save_config()

            return jsonify({
                'success': True,
                'message': f'持仓 {position_id} 已平仓',
                'position': position
            })

    return jsonify({'error': f'找不到持仓 ID: {position_id}'}), 404


@hedging_blueprint.route('/positions', methods=['POST'])
def create_position():
    """
    手动创建新持仓

    请求体:
        JSON格式的持仓数据

    返回:
        JSON格式的创建结果，包含新持仓的ID
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': '无效的请求数据'}), 400

        # 验证必要字段
        required_fields = [
            'strategy_id', 'long_exchange', 'long_symbol', 'long_amount', 'long_price',
            'short_exchange', 'short_symbol', 'short_amount', 'short_price'
        ]
        for field in required_fields:
            if field not in data:
                return jsonify({'error': f'缺少必要字段: {field}'}), 400

        # 创建新持仓
        position_id = _hedging_config['next_position_id']
        _hedging_config['next_position_id'] += 1

        new_position = {
            'id': position_id,
            'strategy_id': data['strategy_id'],
            'long_exchange': data['long_exchange'],
            'long_symbol': data['long_symbol'],
            'long_order_id': data.get('long_order_id', f'manual_{position_id}_long'),
            'long_amount': data['long_amount'],
            'long_price': data['long_price'],
            'short_exchange': data['short_exchange'],
            'short_symbol': data['short_symbol'],
            'short_order_id': data.get('short_order_id', f'manual_{position_id}_short'),
            'short_amount': data['short_amount'],
            'short_price': data['short_price'],
            'status': 'open',
            'opened_at': datetime.now().isoformat(),
            'profit_loss': 0,
            'profit_loss_percent': 0
        }

        _hedging_config['active_positions'].append(new_position)
        _save_config()

        return jsonify(new_position), 201

    except Exception as e:
        return jsonify({'error': f'创建持仓失败: {str(e)}'}), 500


# ==================== 新增：套保条件设置和下单API ====================

@hedging_blueprint.route('/config/templates', methods=['GET'])
def get_condition_templates():
    """
    获取套保条件模板

    返回:
        JSON格式的模板配置
    """
    try:
        from crypt_carry.config.hedging_config_manager import HedgingConfigManager

        config_manager = HedgingConfigManager()
        templates = config_manager.config.get('TEMPLATES', {})

        return jsonify({
            'success': True,
            'templates': templates
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'获取模板失败: {str(e)}'
        }), 500


@hedging_blueprint.route('/config/exchanges', methods=['GET'])
def get_supported_exchanges():
    """
    获取支持的交易所列表

    返回:
        JSON格式的交易所列表
    """
    try:
        from crypt_carry.config.hedging_config_manager import HedgingConfigManager

        config_manager = HedgingConfigManager()
        exchanges = config_manager.exchanges

        return jsonify({
            'success': True,
            'exchanges': exchanges
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'获取交易所列表失败: {str(e)}'
        }), 500


@hedging_blueprint.route('/config/symbols', methods=['GET'])
def get_trading_symbols():
    """
    获取支持的交易对列表

    返回:
        JSON格式的交易对列表
    """
    # 这里应该从交易所API获取实际的交易对列表
    # 暂时返回常用的交易对
    symbols = [
        'BTC/USDT', 'ETH/USDT', 'BNB/USDT', 'ADA/USDT', 'DOT/USDT',
        'SOL/USDT', 'MATIC/USDT', 'AVAX/USDT', 'LINK/USDT', 'UNI/USDT'
    ]

    return jsonify({
        'success': True,
        'symbols': symbols
    })


@hedging_blueprint.route('/orders/hedging', methods=['POST'])
@async_to_sync
async def create_hedging_order():
    """
    创建套保订单

    请求体:
        {
            "conditions": [
                {
                    "type": "price_difference",  // 条件类型
                    "enabled": true,             // 是否启用
                    "trigger_value": 100.0,      // 触发值
                    "comparison_operator": ">"   // 比较运算符
                },
                {
                    "type": "funding_rate_diff",
                    "enabled": true,
                    "trigger_value": 0.001,
                    "comparison_operator": ">"
                },
                {
                    "type": "basis_rate",
                    "enabled": false,
                    "trigger_value": 0.005,
                    "comparison_operator": ">"
                }
            ],
            "long_exchange": "binance",
            "long_symbol": "BTC/USDT",
            "long_is_spot": true,
            "short_exchange": "okx",
            "short_symbol": "BTC/USDT",
            "short_is_spot": false,
            "amount_usdt": 1000.0,
            "priority": 1
        }

    返回:
        JSON格式的创建结果
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({'success': False, 'error': '无效的请求数据'}), 400

        # 验证必要字段 - 更新字段名以匹配前端
        required_fields = [
            'conditions', 'long_exchange', 'long_symbol',
            'short_exchange', 'short_symbol', 'amount', 'amount_currency'
        ]
        for field in required_fields:
            if field not in data:
                return jsonify({'success': False, 'error': f'缺少必要字段: {field}'}), 400

        # 验证条件
        conditions = data['conditions']
        if not conditions or not isinstance(conditions, list):
            return jsonify({'success': False, 'error': '条件列表不能为空'}), 400

        # 检查是否有启用的条件
        enabled_conditions = [c for c in conditions if c.get('enabled', False)]
        if not enabled_conditions:
            return jsonify({'success': False, 'error': '至少需要启用一个条件'}), 400

        # 创建套保条件ID
        condition_id = f"hedging_{int(datetime.now().timestamp())}"

        # 集成实际的套保策略管理器
        try:
            # 计算交易金额（转换为USDT）
            amount_usdt = data['amount']
            if data['amount_currency'] == 'USDC':
                # 简单处理：USDC和USDT按1:1计算，实际应用中可能需要汇率转换
                amount_usdt = data['amount']

            # 为每个启用的条件创建套保条件
            created_conditions = []

            # 尝试使用实际的套保策略管理器，如果失败则使用模拟模式
            try:
                # 获取全局的套保策略管理器实例
                manager = _get_hedging_manager()

                # 初始化管理器（如果还未初始化）
                if not hasattr(manager, 'hedging_strategy') or manager.hedging_strategy is None:
                    await manager.initialize()

                # 实际添加条件到策略管理器
                for i, condition in enumerate(enabled_conditions):
                    condition_type = condition['type']
                    trigger_value = condition['trigger_value']
                    comparison_operator = condition.get('comparison_operator', '>')

                    # 生成唯一的条件ID
                    unique_condition_id = f"{condition_id}_{condition_type}_{i}"

                    success = await manager.add_hedging_condition(
                        condition_id=unique_condition_id,
                        condition_type=condition_type,
                        long_exchange=data['long_exchange'],
                        long_symbol=data['long_symbol'],
                        short_exchange=data['short_exchange'],
                        short_symbol=data['short_symbol'],
                        trigger_value=trigger_value,
                        comparison_operator=comparison_operator,
                        amount_usdt=amount_usdt,
                        long_is_spot=data.get('long_is_spot', True),
                        short_is_spot=data.get('short_is_spot', False),
                        priority=data.get('priority', 1)
                    )

                    if success:
                        condition_data = {
                            'condition_id': unique_condition_id,
                            'condition_type': condition_type,
                            'long_exchange': data['long_exchange'],
                            'long_symbol': data['long_symbol'],
                            'long_is_spot': data.get('long_is_spot', True),
                            'short_exchange': data['short_exchange'],
                            'short_symbol': data['short_symbol'],
                            'short_is_spot': data.get('short_is_spot', False),
                            'trigger_value': trigger_value,
                            'comparison_operator': comparison_operator,
                            'amount_usdt': amount_usdt,
                            'amount_currency': data['amount_currency'],
                            'priority': data.get('priority', 1),
                            'created_at': datetime.now().isoformat(),
                            'status': 'active'
                        }
                        created_conditions.append(condition_data)
                    else:
                        return jsonify({
                            'success': False,
                            'error': f'添加套保条件失败: {unique_condition_id}'
                        }), 500

            except Exception as hedging_error:
                # 如果实际套保管理器失败，使用模拟模式
                print(f"实际套保管理器失败，使用模拟模式: {str(hedging_error)}")

                # 模拟添加条件
                for i, condition in enumerate(enabled_conditions):
                    condition_type = condition['type']
                    trigger_value = condition['trigger_value']
                    comparison_operator = condition.get('comparison_operator', '>')

                    # 生成唯一的条件ID
                    unique_condition_id = f"{condition_id}_{condition_type}_{i}"

                    condition_data = {
                        'condition_id': unique_condition_id,
                        'condition_type': condition_type,
                        'long_exchange': data['long_exchange'],
                        'long_symbol': data['long_symbol'],
                        'long_is_spot': data.get('long_is_spot', True),
                        'short_exchange': data['short_exchange'],
                        'short_symbol': data['short_symbol'],
                        'short_is_spot': data.get('short_is_spot', False),
                        'trigger_value': trigger_value,
                        'comparison_operator': comparison_operator,
                        'amount_usdt': amount_usdt,
                        'amount_currency': data['amount_currency'],
                        'priority': data.get('priority', 1),
                        'created_at': datetime.now().isoformat(),
                        'status': 'active',
                        'note': '模拟模式 - 未实际添加到策略管理器'
                    }
                    created_conditions.append(condition_data)

            result = {
                'success': True,
                'message': '套保订单创建成功',
                'order_id': condition_id,
                'conditions': created_conditions,
                'created_at': datetime.now().isoformat()
            }

            return jsonify(result), 201

        except Exception as e:
            return jsonify({
                'success': False,
                'error': f'创建套保订单失败: {str(e)}'
            }), 500

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'处理请求失败: {str(e)}'
        }), 500


@hedging_blueprint.route('/orders/single', methods=['POST'])
@async_to_sync
async def create_single_order():
    """
    创建单独订单（非套保）

    请求体:
        {
            "exchange": "binance",
            "symbol": "BTC/USDT",
            "side": "buy",           // buy 或 sell
            "order_type": "market",  // market 或 limit
            "amount": 0.001,         // 数量
            "price": 50000.0,        // 价格（限价单时需要）
            "is_spot": true          // true为现货，false为合约
        }

    返回:
        JSON格式的创建结果
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({'success': False, 'error': '无效的请求数据'}), 400

        # 验证必要字段 - 更新以匹配前端数据格式
        required_fields = ['exchange', 'symbol', 'side', 'order_type', 'amount', 'amount_currency']
        for field in required_fields:
            if field not in data:
                return jsonify({'success': False, 'error': f'缺少必要字段: {field}'}), 400

        # 验证订单类型
        if data['order_type'] not in ['market', 'limit']:
            return jsonify({'success': False, 'error': '订单类型必须是 market 或 limit'}), 400

        # 验证买卖方向
        if data['side'] not in ['buy', 'sell']:
            return jsonify({'success': False, 'error': '买卖方向必须是 buy 或 sell'}), 400

        # 限价单需要价格
        if data['order_type'] == 'limit' and 'price' not in data:
            return jsonify({'success': False, 'error': '限价单必须提供价格'}), 400

        # 生成订单ID
        order_id = f"single_{int(datetime.now().timestamp())}"

        # 集成实际的交易执行器
        try:
            # 计算实际交易数量
            amount = data['amount']
            if 'amount_currency' in data:
                # 如果是按金额下单，需要转换为数量
                # 这里简化处理，实际应用中需要根据当前价格计算
                if data['amount_currency'] in ['USDT', 'USDC']:
                    # 假设当前价格，实际应该从市场数据获取
                    current_price = data.get('price', 50000.0)  # 默认价格
                    if data['order_type'] == 'market':
                        # 市价单需要获取实时价格
                        # TODO: 从市场数据管理器获取实时价格
                        pass
                    amount = data['amount'] / current_price

            # 尝试使用实际的订单管理器，如果失败则使用模拟模式
            try:
                # 获取订单管理器实例
                order_manager = _get_order_manager()

                # 执行订单
                order = await order_manager.create_order(
                    exchange=data['exchange'],
                    symbol=data['symbol'],
                    order_type=data['order_type'],
                    side=data['side'],
                    amount=amount,
                    price=data.get('price'),
                    is_spot=data.get('is_spot', True)
                )

                # 实际订单执行成功
                result = {
                    'success': True,
                    'message': '订单提交成功',
                    'order': {
                        'order_id': order.get('id', order_id),
                        'exchange': data['exchange'],
                        'symbol': data['symbol'],
                        'side': data['side'],
                        'order_type': data['order_type'],
                        'amount': amount,
                        'price': data.get('price'),
                        'is_spot': data.get('is_spot', True),
                        'status': order.get('status', 'submitted'),
                        'created_at': datetime.now().isoformat(),
                        'exchange_order_id': order.get('id'),
                        'filled': order.get('filled', 0),
                        'remaining': order.get('remaining', amount),
                        'cost': order.get('cost', 0)
                    }
                }

            except Exception as order_error:
                # 如果实际订单执行失败，使用模拟模式
                print(f"实际订单执行失败，使用模拟模式: {str(order_error)}")

                # 模拟订单执行
                result = {
                    'success': True,
                    'message': '订单提交成功 (模拟模式)',
                    'order': {
                        'order_id': order_id,
                        'exchange': data['exchange'],
                        'symbol': data['symbol'],
                        'side': data['side'],
                        'order_type': data['order_type'],
                        'amount': amount,
                        'price': data.get('price'),
                        'is_spot': data.get('is_spot', True),
                        'status': 'submitted',
                        'created_at': datetime.now().isoformat(),
                        'exchange_order_id': f"mock_{order_id}",
                        'filled': 0,
                        'remaining': amount,
                        'cost': 0,
                        'note': '模拟模式 - 未实际执行交易'
                    }
                }

            return jsonify(result), 201

        except Exception as e:
            return jsonify({
                'success': False,
                'error': f'执行订单失败: {str(e)}'
            }), 500

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'处理请求失败: {str(e)}'
        }), 500


@hedging_blueprint.route('/orders/active', methods=['GET'])
def get_active_orders():
    """
    获取活跃的订单列表

    返回:
        JSON格式的订单列表
    """
    try:
        # TODO: 从实际的订单管理器获取活跃订单
        # 暂时返回模拟数据

        active_orders = [
            {
                'order_id': 'hedging_1234567890',
                'type': 'hedging',
                'status': 'active',
                'conditions': [
                    {'type': 'price_difference', 'trigger_value': 100.0, 'enabled': True},
                    {'type': 'funding_rate_diff', 'trigger_value': 0.001, 'enabled': True}
                ],
                'long_exchange': 'binance',
                'long_symbol': 'BTC/USDT',
                'short_exchange': 'okx',
                'short_symbol': 'BTC/USDT',
                'amount_usdt': 1000.0,
                'created_at': '2024-01-01T10:00:00'
            },
            {
                'order_id': 'single_1234567891',
                'type': 'single',
                'status': 'filled',
                'exchange': 'binance',
                'symbol': 'ETH/USDT',
                'side': 'buy',
                'amount': 0.5,
                'price': 2500.0,
                'created_at': '2024-01-01T09:30:00'
            }
        ]

        return jsonify({
            'success': True,
            'orders': active_orders
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'获取订单列表失败: {str(e)}'
        }), 500


@hedging_blueprint.route('/orders/<order_id>/cancel', methods=['POST'])
def cancel_order(order_id):
    """
    取消订单

    参数:
        order_id: 订单ID

    返回:
        JSON格式的取消结果
    """
    try:
        # TODO: 集成实际的订单取消逻辑

        result = {
            'success': True,
            'message': f'订单 {order_id} 取消成功',
            'order_id': order_id,
            'cancelled_at': datetime.now().isoformat()
        }

        return jsonify(result)

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'取消订单失败: {str(e)}'
        }), 500
