"""
套保策略API路由模块
定义与套保策略和持仓管理相关的API端点
"""
import json
import os
from datetime import datetime

from flask import jsonify, request

from . import hedging_blueprint

# 内存中的配置数据，实际应用中应该使用数据库
_hedging_config = {
    'strategies': [],  # 套保策略列表
    'active_positions': [],  # 活跃持仓列表
    'next_strategy_id': 1,
    'next_position_id': 1,
    'next_condition_id': 1
}

# 配置文件路径
try:
    from crypt_carry.utils.paths import get_web_data_dir

    _CONFIG_FILE = str(get_web_data_dir() / 'hedging_config.json')
except ImportError:
    # 如果无法导入paths模块，使用原来的方法作为备选
    _CONFIG_FILE = os.path.join(os.path.dirname(__file__), '../../data/hedging_config.json')


def _load_config():
    """从文件加载配置"""
    global _hedging_config
    os.makedirs(os.path.dirname(_CONFIG_FILE), exist_ok=True)
    try:
        if os.path.exists(_CONFIG_FILE):
            with open(_CONFIG_FILE, 'r', encoding='utf-8') as f:
                _hedging_config = json.load(f)
    except Exception as e:
        print(f"加载配置失败: {e}")


def _save_config():
    """保存配置到文件"""
    os.makedirs(os.path.dirname(_CONFIG_FILE), exist_ok=True)
    try:
        with open(_CONFIG_FILE, 'w', encoding='utf-8') as f:
            json.dump(_hedging_config, f, ensure_ascii=False, indent=2)
    except Exception as e:
        print(f"保存配置失败: {e}")


# 初始化加载配置
_load_config()


@hedging_blueprint.route('/strategies', methods=['GET'])
def get_strategies():
    """
    获取所有套保策略

    返回:
        JSON格式的策略列表
    """
    return jsonify(_hedging_config['strategies'])


@hedging_blueprint.route('/strategies', methods=['POST'])
def create_strategy():
    """
    创建新的套保策略

    请求体:
        JSON格式的策略数据

    返回:
        JSON格式的创建结果，包含新策略的ID
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': '无效的请求数据'}), 400

        # 验证必要字段
        required_fields = ['name', 'is_active']
        for field in required_fields:
            if field not in data:
                return jsonify({'error': f'缺少必要字段: {field}'}), 400

        # 创建新策略
        strategy_id = _hedging_config['next_strategy_id']
        _hedging_config['next_strategy_id'] += 1

        new_strategy = {
            'id': strategy_id,
            'name': data['name'],
            'description': data.get('description', ''),
            'is_active': data['is_active'],
            'created_at': datetime.now().isoformat(),
            'updated_at': datetime.now().isoformat(),
            'check_interval': data.get('check_interval', 300),
            'auto_close': data.get('auto_close', True),
            'max_open_positions': data.get('max_open_positions', 5),
            'conditions': []
        }

        _hedging_config['strategies'].append(new_strategy)
        _save_config()

        return jsonify(new_strategy), 201

    except Exception as e:
        return jsonify({'error': f'创建策略失败: {str(e)}'}), 500


@hedging_blueprint.route('/strategies/<int:strategy_id>', methods=['GET'])
def get_strategy(strategy_id):
    """
    获取特定的套保策略

    参数:
        strategy_id: 策略ID

    返回:
        JSON格式的策略数据
    """
    for strategy in _hedging_config['strategies']:
        if strategy['id'] == strategy_id:
            return jsonify(strategy)

    return jsonify({'error': f'找不到策略 ID: {strategy_id}'}), 404


@hedging_blueprint.route('/strategies/<int:strategy_id>', methods=['PUT'])
def update_strategy(strategy_id):
    """
    更新特定的套保策略

    参数:
        strategy_id: 策略ID

    请求体:
        JSON格式的策略更新数据

    返回:
        JSON格式的更新结果
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': '无效的请求数据'}), 400

        for i, strategy in enumerate(_hedging_config['strategies']):
            if strategy['id'] == strategy_id:
                # 更新策略字段
                for key, value in data.items():
                    if key != 'id' and key != 'created_at' and key != 'conditions':
                        strategy[key] = value

                strategy['updated_at'] = datetime.now().isoformat()
                _save_config()

                return jsonify(strategy)

        return jsonify({'error': f'找不到策略 ID: {strategy_id}'}), 404

    except Exception as e:
        return jsonify({'error': f'更新策略失败: {str(e)}'}), 500


@hedging_blueprint.route('/strategies/<int:strategy_id>', methods=['DELETE'])
def delete_strategy(strategy_id):
    """
    删除特定的套保策略

    参数:
        strategy_id: 策略ID

    返回:
        JSON格式的删除结果
    """
    for i, strategy in enumerate(_hedging_config['strategies']):
        if strategy['id'] == strategy_id:
            del _hedging_config['strategies'][i]
            _save_config()

            return jsonify({'success': True, 'message': f'策略 ID: {strategy_id} 已删除'})

    return jsonify({'error': f'找不到策略 ID: {strategy_id}'}), 404


@hedging_blueprint.route('/strategies/<int:strategy_id>/conditions', methods=['GET'])
def get_conditions(strategy_id):
    """
    获取特定策略的所有条件

    参数:
        strategy_id: 策略ID

    返回:
        JSON格式的条件列表
    """
    for strategy in _hedging_config['strategies']:
        if strategy['id'] == strategy_id:
            return jsonify(strategy['conditions'])

    return jsonify({'error': f'找不到策略 ID: {strategy_id}'}), 404


@hedging_blueprint.route('/strategies/<int:strategy_id>/conditions', methods=['POST'])
def create_condition(strategy_id):
    """
    为特定策略创建新条件

    参数:
        strategy_id: 策略ID

    请求体:
        JSON格式的条件数据

    返回:
        JSON格式的创建结果，包含新条件的ID
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': '无效的请求数据'}), 400

        # 验证必要字段
        required_fields = [
            'condition_type', 'long_exchange', 'long_symbol', 'long_amount',
            'short_exchange', 'short_symbol', 'short_amount', 'trigger_value',
            'comparison_operator'
        ]
        for field in required_fields:
            if field not in data:
                return jsonify({'error': f'缺少必要字段: {field}'}), 400

        for i, strategy in enumerate(_hedging_config['strategies']):
            if strategy['id'] == strategy_id:
                # 创建新条件
                condition_id = _hedging_config['next_condition_id']
                _hedging_config['next_condition_id'] += 1

                new_condition = {
                    'id': condition_id,
                    'condition_type': data['condition_type'],
                    'long_exchange': data['long_exchange'],
                    'long_symbol': data['long_symbol'],
                    'long_amount': data['long_amount'],
                    'long_is_spot': data.get('long_is_spot', True),
                    'short_exchange': data['short_exchange'],
                    'short_symbol': data['short_symbol'],
                    'short_amount': data['short_amount'],
                    'short_is_spot': data.get('short_is_spot', False),
                    'trigger_value': data['trigger_value'],
                    'comparison_operator': data['comparison_operator'],
                    'is_active': data.get('is_active', True),
                    'priority': data.get('priority', 1)
                }

                strategy['conditions'].append(new_condition)
                _save_config()

                return jsonify(new_condition), 201

        return jsonify({'error': f'找不到策略 ID: {strategy_id}'}), 404

    except Exception as e:
        return jsonify({'error': f'创建条件失败: {str(e)}'}), 500


@hedging_blueprint.route('/strategies/<int:strategy_id>/conditions/<int:condition_id>', methods=['PUT'])
def update_condition(strategy_id, condition_id):
    """
    更新特定条件

    参数:
        strategy_id: 策略ID
        condition_id: 条件ID

    请求体:
        JSON格式的条件更新数据

    返回:
        JSON格式的更新结果
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': '无效的请求数据'}), 400

        for strategy in _hedging_config['strategies']:
            if strategy['id'] == strategy_id:
                for i, condition in enumerate(strategy['conditions']):
                    if condition['id'] == condition_id:
                        # 更新条件字段
                        for key, value in data.items():
                            if key != 'id':
                                condition[key] = value

                        _save_config()

                        return jsonify(condition)

                return jsonify({'error': f'找不到条件 ID: {condition_id}'}), 404

        return jsonify({'error': f'找不到策略 ID: {strategy_id}'}), 404

    except Exception as e:
        return jsonify({'error': f'更新条件失败: {str(e)}'}), 500


@hedging_blueprint.route('/strategies/<int:strategy_id>/conditions/<int:condition_id>', methods=['DELETE'])
def delete_condition(strategy_id, condition_id):
    """
    删除特定条件

    参数:
        strategy_id: 策略ID
        condition_id: 条件ID

    返回:
        JSON格式的删除结果
    """
    for strategy in _hedging_config['strategies']:
        if strategy['id'] == strategy_id:
            for i, condition in enumerate(strategy['conditions']):
                if condition['id'] == condition_id:
                    del strategy['conditions'][i]
                    _save_config()

                    return jsonify({'success': True, 'message': f'条件 ID: {condition_id} 已删除'})

            return jsonify({'error': f'找不到条件 ID: {condition_id}'}), 404

    return jsonify({'error': f'找不到策略 ID: {strategy_id}'}), 404


@hedging_blueprint.route('/strategies/<int:strategy_id>/execute', methods=['POST'])
def execute_strategy(strategy_id):
    """
    手动执行特定策略

    参数:
        strategy_id: 策略ID

    返回:
        JSON格式的执行结果
    """
    for strategy in _hedging_config['strategies']:
        if strategy['id'] == strategy_id:
            if not strategy['is_active']:
                return jsonify({'error': '策略未激活，无法执行'}), 400

            # TODO: 实际执行策略的代码
            # 这里需要调用您现有的套保策略执行代码

            # 模拟执行结果
            result = {
                'success': True,
                'strategy_id': strategy_id,
                'message': f'策略 {strategy["name"]} 执行成功',
                'execution_time': datetime.now().isoformat(),
            }

            return jsonify(result)

    return jsonify({'error': f'找不到策略 ID: {strategy_id}'}), 404


@hedging_blueprint.route('/positions', methods=['GET'])
def get_positions():
    """
    获取所有活跃的套保持仓

    返回:
        JSON格式的持仓列表
    """
    return jsonify(_hedging_config['active_positions'])


@hedging_blueprint.route('/positions/<int:position_id>/close', methods=['POST'])
def close_position(position_id):
    """
    关闭特定的套保持仓

    参数:
        position_id: 持仓ID

    返回:
        JSON格式的平仓结果
    """
    for i, position in enumerate(_hedging_config['active_positions']):
        if position['id'] == position_id:
            # TODO: 实际平仓代码
            # 这里需要调用您现有的平仓功能代码

            # 从活跃持仓中移除
            position = _hedging_config['active_positions'].pop(i)
            position['status'] = 'closed'
            position['closed_at'] = datetime.now().isoformat()
            _save_config()

            return jsonify({
                'success': True,
                'message': f'持仓 {position_id} 已平仓',
                'position': position
            })

    return jsonify({'error': f'找不到持仓 ID: {position_id}'}), 404


@hedging_blueprint.route('/positions', methods=['POST'])
def create_position():
    """
    手动创建新持仓

    请求体:
        JSON格式的持仓数据

    返回:
        JSON格式的创建结果，包含新持仓的ID
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': '无效的请求数据'}), 400

        # 验证必要字段
        required_fields = [
            'strategy_id', 'long_exchange', 'long_symbol', 'long_amount', 'long_price',
            'short_exchange', 'short_symbol', 'short_amount', 'short_price'
        ]
        for field in required_fields:
            if field not in data:
                return jsonify({'error': f'缺少必要字段: {field}'}), 400

        # 创建新持仓
        position_id = _hedging_config['next_position_id']
        _hedging_config['next_position_id'] += 1

        new_position = {
            'id': position_id,
            'strategy_id': data['strategy_id'],
            'long_exchange': data['long_exchange'],
            'long_symbol': data['long_symbol'],
            'long_order_id': data.get('long_order_id', f'manual_{position_id}_long'),
            'long_amount': data['long_amount'],
            'long_price': data['long_price'],
            'short_exchange': data['short_exchange'],
            'short_symbol': data['short_symbol'],
            'short_order_id': data.get('short_order_id', f'manual_{position_id}_short'),
            'short_amount': data['short_amount'],
            'short_price': data['short_price'],
            'status': 'open',
            'opened_at': datetime.now().isoformat(),
            'profit_loss': 0,
            'profit_loss_percent': 0
        }

        _hedging_config['active_positions'].append(new_position)
        _save_config()

        return jsonify(new_position), 201

    except Exception as e:
        return jsonify({'error': f'创建持仓失败: {str(e)}'}), 500
