"""
套保策略API路由模块（简化版）
只保留核心的下单功能，移除复杂的策略管理接口
"""
import json
import os
import asyncio
import functools
from datetime import datetime
from typing import Dict, List, Optional

from flask import jsonify, request

from . import hedging_blueprint


def async_to_sync(f):
    """异步转同步装饰器"""
    @functools.wraps(f)
    def wrapper(*args, **kwargs):
        return asyncio.run(f(*args, **kwargs))
    return wrapper


# ==================== 配置管理 ====================

# 本地存储路径（使用JSON文件代替数据库）
try:
    from crypt_carry.utils.paths import get_web_data_dir
    STORAGE_PATH = str(get_web_data_dir())
    HEDGING_CONFIG_FILE = str(get_web_data_dir() / 'hedging_config.json')
except ImportError:
    # 如果无法导入paths模块，使用原来的方法作为备选
    STORAGE_PATH = os.path.join(os.path.dirname(__file__), '..', 'data')
    HEDGING_CONFIG_FILE = os.path.join(STORAGE_PATH, 'hedging_config.json')

# 确保存储目录存在
os.makedirs(STORAGE_PATH, exist_ok=True)

# 内存中的套保配置
_hedging_config = {
    'active_positions': [],
    'next_position_id': 1
}


def _save_config():
    """保存套保配置到本地文件"""
    try:
        with open(HEDGING_CONFIG_FILE, 'w', encoding='utf-8') as f:
            json.dump(_hedging_config, f, ensure_ascii=False, indent=2)
        return True
    except Exception as e:
        print(f"保存套保配置失败: {str(e)}")
        return False


def _load_config():
    """从本地文件加载套保配置"""
    global _hedging_config
    if os.path.exists(HEDGING_CONFIG_FILE):
        try:
            with open(HEDGING_CONFIG_FILE, 'r', encoding='utf-8') as f:
                _hedging_config = json.load(f)
        except Exception as e:
            print(f"加载套保配置失败: {str(e)}")


# 初始化加载配置
_load_config()

# 全局套保策略管理器实例
_global_hedging_manager = None


def _get_hedging_manager():
    """获取全局套保策略管理器实例"""
    global _global_hedging_manager
    if _global_hedging_manager is None:
        from crypt_carry.strategies.hedging.hedging_manager import HedgingStrategyManager
        _global_hedging_manager = HedgingStrategyManager()
    return _global_hedging_manager


def _get_order_manager():
    """获取全局订单管理器实例"""
    from crypt_carry.core.exchange.account.order_manager import OrderManager
    return OrderManager.get_instance()


# ==================== 核心下单接口 ====================

@hedging_blueprint.route('/orders/hedging', methods=['POST'])
@async_to_sync
async def create_hedging_order():
    """
    创建套保订单

    这是核心的套保下单接口，根据前端提交的条件创建跨交易所套保订单
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({'success': False, 'error': '无效的请求数据'}), 400

        # 生成唯一的订单ID
        order_id = f"hedging_{int(datetime.now().timestamp())}"

        # 验证必要字段 - 更新字段名以匹配前端
        required_fields = [
            'conditions', 'long_exchange', 'long_symbol',
            'short_exchange', 'short_symbol', 'amount', 'amount_currency'
        ]
        for field in required_fields:
            if field not in data:
                return jsonify({'success': False, 'error': f'缺少必要字段: {field}'}), 400

        # 解析条件
        conditions = data['conditions']
        if not isinstance(conditions, list):
            return jsonify({'success': False, 'error': '条件参数必须是数组格式'}), 400

        # 过滤启用的条件
        enabled_conditions = [c for c in conditions if c.get('enabled', False)]

        # 交易金额（根据amount_currency确定实际金额）
        amount = data['amount']
        amount_currency = data['amount_currency']

        # 统一交给策略管理器处理，不在API层判断执行时机
        created_conditions = []

        # 尝试使用实际的套保策略管理器，如果失败则使用模拟模式
        try:
            # 获取全局的套保策略管理器实例
            manager = _get_hedging_manager()

            # 初始化管理器（如果还未初始化）
            if not hasattr(manager, 'hedging_strategy') or manager.hedging_strategy is None:
                await manager.initialize()

            # 统一调用策略管理器的套保订单创建方法
            # 让策略层决定是立即执行还是添加条件监控
            hedging_order_result = await manager.create_hedging_order(
                order_id=order_id,
                conditions=enabled_conditions,  # 传递启用的条件列表
                long_exchange=data['long_exchange'],
                long_symbol=data['long_symbol'],
                short_exchange=data['short_exchange'],
                short_symbol=data['short_symbol'],
                amount=amount,
                amount_currency=amount_currency,
                long_is_spot=data.get('long_is_spot', True),
                short_is_spot=data.get('short_is_spot', False),
                priority=data.get('priority', 1)
            )

            if hedging_order_result['success']:
                created_conditions = hedging_order_result['conditions']
            else:
                return jsonify({
                    'success': False,
                    'error': hedging_order_result['error']
                }), 500

            # # 启动策略监控（如果还未启动）
            # if created_conditions and not manager.hedging_strategy.is_running:
            #     await manager.start_strategy()
            #     print("套保策略监控已启动")

        except Exception as hedging_error:
            # 如果实际套保管理器失败，使用模拟模式
            print(f"实际套保管理器失败，使用模拟模式: {str(hedging_error)}")

            # 模拟策略管理器的处理逻辑
            if len(enabled_conditions) == 0:
                # 模拟立即执行套保
                execution_data = {
                    'execution_id': f"{order_id}_immediate",
                    'execution_type': 'immediate',
                    'long_exchange': data['long_exchange'],
                    'long_symbol': data['long_symbol'],
                    'long_is_spot': data.get('long_is_spot', True),
                    'short_exchange': data['short_exchange'],
                    'short_symbol': data['short_symbol'],
                    'short_is_spot': data.get('short_is_spot', False),
                    'amount': amount,
                    'amount_currency': amount_currency,
                    'status': 'executed',
                    'executed_at': datetime.now().isoformat(),
                    'result': {
                        'success': True,
                        'message': '立即套保执行成功 (模拟模式)',
                        'hedging_pair': {
                            'long_order': {'exchange': data['long_exchange'], 'symbol': data['long_symbol'], 'status': 'simulated'},
                            'short_order': {'exchange': data['short_exchange'], 'symbol': data['short_symbol'], 'status': 'simulated'}
                        },
                        'note': '模拟模式 - 未实际执行交易'
                    }
                }
                created_conditions.append(execution_data)
            else:
                # 模拟添加组合条件（所有条件作为一个整体）
                condition_data = {
                    'condition_id': f"{order_id}_combined",
                    'condition_type': 'combined',
                    'conditions': enabled_conditions,  # 保存所有条件
                    'long_exchange': data['long_exchange'],
                    'long_symbol': data['long_symbol'],
                    'long_is_spot': data.get('long_is_spot', True),
                    'short_exchange': data['short_exchange'],
                    'short_symbol': data['short_symbol'],
                    'short_is_spot': data.get('short_is_spot', False),
                    'amount': amount,
                    'amount_currency': amount_currency,
                    'priority': data.get('priority', 1),
                    'created_at': datetime.now().isoformat(),
                    'status': 'monitoring',
                    'note': '模拟模式 - 未实际添加到策略管理器',
                    'logic': 'ALL_CONDITIONS_MUST_BE_MET'  # 标明逻辑：所有条件都必须满足
                }
                created_conditions.append(condition_data)

        # 构建返回结果
        # 根据创建的条件类型判断执行模式
        is_immediate = (created_conditions and
                       created_conditions[0].get('execution_type') == 'immediate')

        if is_immediate:
            # 立即执行的情况
            execution_status = "已立即执行" if created_conditions else "执行失败"
            result = {
                'success': True,
                'message': f'套保订单创建成功，{execution_status}',
                'order_id': order_id,
                'execution_type': 'immediate',
                'conditions': created_conditions,
                'execution_status': execution_status,
                'next_steps': [
                    '1. 套保已立即执行完成',
                    '2. 可通过API查询执行结果',
                    '3. 查看持仓管理器了解当前持仓状态'
                ],
                'created_at': datetime.now().isoformat()
            }
        else:
            # 条件监控的情况
            monitoring_status = "已启动监控" if created_conditions else "监控启动失败"
            result = {
                'success': True,
                'message': f'套保订单创建成功，{monitoring_status}',
                'order_id': order_id,
                'execution_type': 'conditional',
                'conditions': created_conditions,
                'monitoring_status': monitoring_status,
                'next_steps': [
                    '1. 系统将持续监控市场条件',
                    '2. 当满足所有触发条件时自动执行套保',
                    '3. 可通过API查询执行状态'
                ],
                'created_at': datetime.now().isoformat()
            }

        return jsonify(result), 201

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'处理请求失败: {str(e)}'
        }), 500


@hedging_blueprint.route('/orders/single', methods=['POST'])
@async_to_sync
async def create_single_order():
    """
    创建单独订单（非套保）

    这是核心的单独下单接口，在指定交易所创建单个订单
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({'success': False, 'error': '无效的请求数据'}), 400

        # 生成唯一的订单ID
        order_id = f"single_{int(datetime.now().timestamp())}"

        # 验证必要字段 - 更新以匹配前端数据格式
        required_fields = ['exchange', 'symbol', 'side', 'order_type', 'amount', 'amount_currency']
        for field in required_fields:
            if field not in data:
                return jsonify({'success': False, 'error': f'缺少必要字段: {field}'}), 400

        # 集成实际的交易执行器
        try:
            # 计算实际交易数量
            amount = data['amount']
            if 'amount_currency' in data:
                # 如果是按金额下单，需要转换为数量
                # 这里简化处理，实际应用中需要根据当前价格计算
                if data['amount_currency'] in ['USDT', 'USDC']:
                    # 假设当前价格，实际应该从市场数据获取
                    current_price = data.get('price', 50000.0)  # 默认价格
                    if data['order_type'] == 'market':
                        # 市价单需要获取实时价格
                        # TODO: 从市场数据管理器获取实时价格
                        pass
                    amount = data['amount'] / current_price

            # 尝试使用实际的订单管理器，如果失败则使用模拟模式
            try:
                # 获取订单管理器实例
                order_manager = _get_order_manager()

                # 执行订单
                order = await order_manager.create_order(
                    exchange=data['exchange'],
                    symbol=data['symbol'],
                    order_type=data['order_type'],
                    side=data['side'],
                    amount=amount,
                    price=data.get('price'),
                    is_spot=data.get('is_spot', True)
                )

                # 实际订单执行成功
                result = {
                    'success': True,
                    'message': '订单提交成功',
                    'order': {
                        'order_id': order.get('id', order_id),
                        'exchange': data['exchange'],
                        'symbol': data['symbol'],
                        'side': data['side'],
                        'order_type': data['order_type'],
                        'amount': amount,
                        'price': data.get('price'),
                        'is_spot': data.get('is_spot', True),
                        'status': order.get('status', 'submitted'),
                        'created_at': datetime.now().isoformat(),
                        'exchange_order_id': order.get('id'),
                        'filled': order.get('filled', 0),
                        'remaining': order.get('remaining', amount),
                        'cost': order.get('cost', 0)
                    }
                }

            except Exception as order_error:
                # 如果实际订单执行失败，使用模拟模式
                print(f"实际订单执行失败，使用模拟模式: {str(order_error)}")

                # 模拟订单执行
                result = {
                    'success': True,
                    'message': '订单提交成功 (模拟模式)',
                    'order': {
                        'order_id': order_id,
                        'exchange': data['exchange'],
                        'symbol': data['symbol'],
                        'side': data['side'],
                        'order_type': data['order_type'],
                        'amount': amount,
                        'price': data.get('price'),
                        'is_spot': data.get('is_spot', True),
                        'status': 'submitted',
                        'created_at': datetime.now().isoformat(),
                        'exchange_order_id': f"mock_{order_id}",
                        'filled': 0,
                        'remaining': amount,
                        'cost': 0,
                        'note': '模拟模式 - 未实际执行交易'
                    }
                }

            return jsonify(result), 201

        except Exception as e:
            return jsonify({
                'success': False,
                'error': f'订单执行失败: {str(e)}'
            }), 500

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'处理请求失败: {str(e)}'
        }), 500


# ==================== 辅助接口 ====================

@hedging_blueprint.route('/config/symbols', methods=['GET'])
def get_trading_symbols():
    """
    获取支持的交易对列表
    """
    # 这里应该从交易所API获取实际的交易对列表
    # 暂时返回常用的交易对
    symbols = [
        'BTC/USDT', 'ETH/USDT', 'BNB/USDT', 'ADA/USDT', 'DOT/USDT',
        'SOL/USDT', 'MATIC/USDT', 'AVAX/USDT', 'LINK/USDT', 'UNI/USDT'
    ]

    return jsonify({
        'success': True,
        'symbols': symbols
    })


@hedging_blueprint.route('/orders/hedging/status', methods=['GET'])
def get_hedging_status():
    """
    查询套保条件的监控状态

    返回当前所有活跃的套保条件及其状态
    """
    try:
        # 获取全局的套保策略管理器实例
        manager = _get_hedging_manager()

        if not manager or not manager.hedging_strategy:
            return jsonify({
                'success': True,
                'message': '套保策略管理器未初始化',
                'is_running': False,
                'conditions': [],
                'total_conditions': 0
            })

        # 获取策略运行状态
        is_running = manager.hedging_strategy.is_running
        conditions = manager.hedging_strategy.conditions

        # 构建条件状态信息
        condition_status = []
        for condition_id, condition in conditions.items():
            condition_info = {
                'condition_id': condition_id,
                'condition_type': condition.condition_type.value,
                'long_exchange': condition.long_exchange,
                'long_symbol': condition.long_symbol,
                'short_exchange': condition.short_exchange,
                'short_symbol': condition.short_symbol,
                'trigger_value': condition.trigger_value,
                'comparison_operator': condition.comparison_operator,
                'amount': condition.amount,
                'amount_currency': condition.amount_currency,
                'is_active': condition.is_active,
                'priority': condition.priority,
                'created_at': condition.created_at.isoformat() if condition.created_at else None,
                'status': 'monitoring' if condition.is_active else 'inactive'
            }
            condition_status.append(condition_info)

        return jsonify({
            'success': True,
            'message': '套保状态查询成功',
            'is_running': is_running,
            'conditions': condition_status,
            'total_conditions': len(condition_status),
            'active_conditions': len([c for c in condition_status if c['is_active']]),
            'strategy_info': {
                'check_interval': manager.hedging_strategy.check_interval,
                'max_positions': manager.hedging_strategy.max_positions,
                'min_amount': manager.hedging_strategy.min_amount,
                'max_amount': manager.hedging_strategy.max_amount
            }
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'查询套保状态失败: {str(e)}'
        }), 500


@hedging_blueprint.route('/orders/hedging/<condition_id>', methods=['DELETE'])
@async_to_sync
async def remove_hedging_condition(condition_id):
    """
    移除指定的套保条件

    Args:
        condition_id: 条件ID
    """
    try:
        # 获取全局的套保策略管理器实例
        manager = _get_hedging_manager()

        if not manager or not manager.hedging_strategy:
            return jsonify({
                'success': False,
                'error': '套保策略管理器未初始化'
            }), 400

        # 移除条件
        success = await manager.remove_hedging_condition(condition_id)

        if success:
            return jsonify({
                'success': True,
                'message': f'套保条件 {condition_id} 已移除',
                'condition_id': condition_id
            })
        else:
            return jsonify({
                'success': False,
                'error': f'移除套保条件失败: {condition_id}'
            }), 404

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'移除套保条件失败: {str(e)}'
        }), 500


# ==================== 新架构API接口 ====================

@hedging_blueprint.route('/orders/<order_id>/status', methods=['GET'])
@async_to_sync
async def get_order_status(order_id: str):
    """获取订单状态

    Args:
        order_id: 订单ID

    Returns:
        订单状态信息
    """
    try:
        print(f"查询订单状态: {order_id}")

        # 导入订单缓存管理器
        from crypt_carry.strategies.hedging.order_cache_manager import get_order_cache_manager

        order_cache = get_order_cache_manager()

        # 获取订单摘要
        order_summary = await order_cache.get_order_summary(order_id)

        if not order_summary:
            return jsonify({
                'success': False,
                'error': f'订单不存在: {order_id}'
            }), 404

        # 获取详细的订单信息
        order = await order_cache.get_order(order_id)

        if order:
            # 构建详细状态信息
            result = {
                'success': True,
                'order_id': order_id,
                'summary': order_summary,
                'details': {
                    'execution_type': order.execution_type.value,
                    'conditions': order.conditions,
                    'long_exchange': order.long_exchange,
                    'long_symbol': order.long_symbol,
                    'long_is_spot': order.long_is_spot,
                    'short_exchange': order.short_exchange,
                    'short_symbol': order.short_symbol,
                    'short_is_spot': order.short_is_spot,
                    'amount': order.amount,
                    'amount_currency': order.amount_currency,
                    'priority': order.priority,
                    'split_tasks': order.split_tasks,
                    'execution_results': order.execution_results
                }
            }
        else:
            result = {
                'success': True,
                'order_id': order_id,
                'summary': order_summary,
                'details': None
            }

        return jsonify(result), 200

    except Exception as e:
        print(f"查询订单状态失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'查询订单状态失败: {str(e)}'
        }), 500


@hedging_blueprint.route('/orders/list', methods=['GET'])
@async_to_sync
async def list_orders():
    """获取订单列表

    Returns:
        订单列表
    """
    try:
        print("查询订单列表")

        # 获取查询参数
        status = request.args.get('status')  # 可选的状态过滤
        limit = int(request.args.get('limit', 50))  # 限制数量

        # 导入订单缓存管理器
        from crypt_carry.strategies.hedging.order_cache_manager import get_order_cache_manager, OrderStatus

        order_cache = get_order_cache_manager()

        if status:
            # 根据状态过滤
            try:
                status_enum = OrderStatus(status)
                orders = await order_cache.get_orders_by_status(status_enum)
            except ValueError:
                return jsonify({
                    'success': False,
                    'error': f'无效的订单状态: {status}'
                }), 400
        else:
            # 获取所有订单
            orders = list(order_cache.orders.values())

        # 限制数量并按创建时间倒序排列
        orders = sorted(orders, key=lambda x: x.created_at or datetime.min, reverse=True)[:limit]

        # 构建订单摘要列表
        order_summaries = []
        for order in orders:
            summary = await order_cache.get_order_summary(order.order_id)
            if summary:
                order_summaries.append(summary)

        result = {
            'success': True,
            'orders': order_summaries,
            'total_count': len(order_summaries),
            'filter_status': status,
            'limit': limit
        }

        return jsonify(result), 200

    except Exception as e:
        print(f"查询订单列表失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'查询订单列表失败: {str(e)}'
        }), 500


@hedging_blueprint.route('/executor/status', methods=['GET'])
@async_to_sync
async def get_executor_status():
    """获取任务执行器状态

    Returns:
        执行器状态信息
    """
    try:
        print("查询任务执行器状态")

        # 导入任务执行器
        from crypt_carry.strategies.hedging.task_executor import get_task_executor

        task_executor = get_task_executor()

        # 获取执行器状态
        status = await task_executor.get_execution_status()

        result = {
            'success': True,
            'executor_status': status
        }

        return jsonify(result), 200

    except Exception as e:
        print(f"查询任务执行器状态失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'查询任务执行器状态失败: {str(e)}'
        }), 500


@hedging_blueprint.route('/tasks/<task_id>/execute', methods=['POST'])
@async_to_sync
async def force_execute_task(task_id: str):
    """强制执行指定任务

    Args:
        task_id: 任务ID

    Returns:
        执行结果
    """
    try:
        print(f"强制执行任务: {task_id}")

        # 导入任务执行器
        from crypt_carry.strategies.hedging.task_executor import get_task_executor

        task_executor = get_task_executor()

        # 强制执行任务
        result = await task_executor.force_execute_task(task_id)

        return jsonify(result), 200

    except Exception as e:
        print(f"强制执行任务失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'强制执行任务失败: {str(e)}'
        }), 500