"""
套保策略API路由模块（简化版）
只保留核心的下单功能，移除复杂的策略管理接口
"""
import json
import os
import asyncio
import functools
from datetime import datetime
from typing import Dict, List, Optional

from flask import jsonify, request

from . import hedging_blueprint


def async_to_sync(f):
    """异步转同步装饰器"""
    @functools.wraps(f)
    def wrapper(*args, **kwargs):
        return asyncio.run(f(*args, **kwargs))
    return wrapper


# ==================== 配置管理 ====================

# 本地存储路径（使用JSON文件代替数据库）
try:
    from crypt_carry.utils.paths import get_web_data_dir
    STORAGE_PATH = str(get_web_data_dir())
    HEDGING_CONFIG_FILE = str(get_web_data_dir() / 'hedging_config.json')
except ImportError:
    # 如果无法导入paths模块，使用原来的方法作为备选
    STORAGE_PATH = os.path.join(os.path.dirname(__file__), '..', 'data')
    HEDGING_CONFIG_FILE = os.path.join(STORAGE_PATH, 'hedging_config.json')

# 确保存储目录存在
os.makedirs(STORAGE_PATH, exist_ok=True)

# 内存中的套保配置
_hedging_config = {
    'active_positions': [],
    'next_position_id': 1
}


def _save_config():
    """保存套保配置到本地文件"""
    try:
        with open(HEDGING_CONFIG_FILE, 'w', encoding='utf-8') as f:
            json.dump(_hedging_config, f, ensure_ascii=False, indent=2)
        return True
    except Exception as e:
        print(f"保存套保配置失败: {str(e)}")
        return False


def _load_config():
    """从本地文件加载套保配置"""
    global _hedging_config
    if os.path.exists(HEDGING_CONFIG_FILE):
        try:
            with open(HEDGING_CONFIG_FILE, 'r', encoding='utf-8') as f:
                _hedging_config = json.load(f)
        except Exception as e:
            print(f"加载套保配置失败: {str(e)}")


# 初始化加载配置
_load_config()

# 全局套保策略管理器实例
_global_hedging_manager = None


def _get_hedging_manager():
    """获取全局套保策略管理器实例"""
    global _global_hedging_manager
    if _global_hedging_manager is None:
        from crypt_carry.strategies.hedging.hedging_manager import HedgingStrategyManager
        _global_hedging_manager = HedgingStrategyManager()
    return _global_hedging_manager


def _get_order_manager():
    """获取全局订单管理器实例"""
    from crypt_carry.core.exchange.account.order_manager import OrderManager
    return OrderManager.get_instance()


# ==================== 核心下单接口 ====================

@hedging_blueprint.route('/orders/hedging', methods=['POST'])
@async_to_sync
async def create_hedging_order():
    """
    创建套保订单
    
    这是核心的套保下单接口，根据前端提交的条件创建跨交易所套保订单
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({'success': False, 'error': '无效的请求数据'}), 400

        # 生成唯一的订单ID
        order_id = f"hedging_{int(datetime.now().timestamp())}"

        # 验证必要字段 - 更新字段名以匹配前端
        required_fields = [
            'conditions', 'long_exchange', 'long_symbol',
            'short_exchange', 'short_symbol', 'amount', 'amount_currency'
        ]
        for field in required_fields:
            if field not in data:
                return jsonify({'success': False, 'error': f'缺少必要字段: {field}'}), 400

        # 解析条件
        conditions = data['conditions']
        if not isinstance(conditions, list) or len(conditions) == 0:
            return jsonify({'success': False, 'error': '至少需要一个套保条件'}), 400

        # 过滤启用的条件
        enabled_conditions = [c for c in conditions if c.get('enabled', False)]
        if len(enabled_conditions) == 0:
            return jsonify({'success': False, 'error': '至少需要启用一个套保条件'}), 400

        # 交易金额
        amount_usdt = data['amount']


        # 为每个启用的条件创建套保条件
        created_conditions = []
        
        # 尝试使用实际的套保策略管理器，如果失败则使用模拟模式
        try:
            # 获取全局的套保策略管理器实例
            manager = _get_hedging_manager()
            
            # 初始化管理器（如果还未初始化）
            if not hasattr(manager, 'hedging_strategy') or manager.hedging_strategy is None:
                await manager.initialize()
            
            # 实际添加条件到策略管理器
            for i, condition in enumerate(enabled_conditions):
                condition_type = condition['type']
                trigger_value = condition['trigger_value']
                comparison_operator = condition.get('comparison_operator', '>')

                # 生成唯一的条件ID
                unique_condition_id = f"{order_id}_{condition_type}_{i}"

                success = await manager.add_hedging_condition(
                    condition_id=unique_condition_id,
                    condition_type=condition_type,
                    long_exchange=data['long_exchange'],
                    long_symbol=data['long_symbol'],
                    short_exchange=data['short_exchange'],
                    short_symbol=data['short_symbol'],
                    trigger_value=trigger_value,
                    comparison_operator=comparison_operator,
                    amount_usdt=amount_usdt,
                    long_is_spot=data.get('long_is_spot', True),
                    short_is_spot=data.get('short_is_spot', False),
                    priority=data.get('priority', 1)
                )

                if success:
                    condition_data = {
                        'condition_id': unique_condition_id,
                        'condition_type': condition_type,
                        'long_exchange': data['long_exchange'],
                        'long_symbol': data['long_symbol'],
                        'long_is_spot': data.get('long_is_spot', True),
                        'short_exchange': data['short_exchange'],
                        'short_symbol': data['short_symbol'],
                        'short_is_spot': data.get('short_is_spot', False),
                        'trigger_value': trigger_value,
                        'comparison_operator': comparison_operator,
                        'amount_usdt': amount_usdt,
                        'amount_currency': data['amount_currency'],
                        'priority': data.get('priority', 1),
                        'created_at': datetime.now().isoformat(),
                        'status': 'active'
                    }
                    created_conditions.append(condition_data)
                else:
                    return jsonify({
                        'success': False,
                        'error': f'添加套保条件失败: {unique_condition_id}'
                    }), 500
                    
        except Exception as hedging_error:
            # 如果实际套保管理器失败，使用模拟模式
            print(f"实际套保管理器失败，使用模拟模式: {str(hedging_error)}")
            
            # 模拟添加条件
            for i, condition in enumerate(enabled_conditions):
                condition_type = condition['type']
                trigger_value = condition['trigger_value']
                comparison_operator = condition.get('comparison_operator', '>')

                # 生成唯一的条件ID
                unique_condition_id = f"{order_id}_{condition_type}_{i}"

                condition_data = {
                    'condition_id': unique_condition_id,
                    'condition_type': condition_type,
                    'long_exchange': data['long_exchange'],
                    'long_symbol': data['long_symbol'],
                    'long_is_spot': data.get('long_is_spot', True),
                    'short_exchange': data['short_exchange'],
                    'short_symbol': data['short_symbol'],
                    'short_is_spot': data.get('short_is_spot', False),
                    'trigger_value': trigger_value,
                    'comparison_operator': comparison_operator,
                    'amount_usdt': amount_usdt,
                    'amount_currency': data['amount_currency'],
                    'priority': data.get('priority', 1),
                    'created_at': datetime.now().isoformat(),
                    'status': 'active',
                    'note': '模拟模式 - 未实际添加到策略管理器'
                }
                created_conditions.append(condition_data)

        result = {
            'success': True,
            'message': '套保订单创建成功',
            'order_id': order_id,
            'conditions': created_conditions,
            'created_at': datetime.now().isoformat()
        }

        return jsonify(result), 201

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'处理请求失败: {str(e)}'
        }), 500


@hedging_blueprint.route('/orders/single', methods=['POST'])
@async_to_sync
async def create_single_order():
    """
    创建单独订单（非套保）
    
    这是核心的单独下单接口，在指定交易所创建单个订单
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({'success': False, 'error': '无效的请求数据'}), 400

        # 生成唯一的订单ID
        order_id = f"single_{int(datetime.now().timestamp())}"

        # 验证必要字段 - 更新以匹配前端数据格式
        required_fields = ['exchange', 'symbol', 'side', 'order_type', 'amount', 'amount_currency']
        for field in required_fields:
            if field not in data:
                return jsonify({'success': False, 'error': f'缺少必要字段: {field}'}), 400

        # 集成实际的交易执行器
        try:
            # 计算实际交易数量
            amount = data['amount']
            if 'amount_currency' in data:
                # 如果是按金额下单，需要转换为数量
                # 这里简化处理，实际应用中需要根据当前价格计算
                if data['amount_currency'] in ['USDT', 'USDC']:
                    # 假设当前价格，实际应该从市场数据获取
                    current_price = data.get('price', 50000.0)  # 默认价格
                    if data['order_type'] == 'market':
                        # 市价单需要获取实时价格
                        # TODO: 从市场数据管理器获取实时价格
                        pass
                    amount = data['amount'] / current_price

            # 尝试使用实际的订单管理器，如果失败则使用模拟模式
            try:
                # 获取订单管理器实例
                order_manager = _get_order_manager()
                
                # 执行订单
                order = await order_manager.create_order(
                    exchange=data['exchange'],
                    symbol=data['symbol'],
                    order_type=data['order_type'],
                    side=data['side'],
                    amount=amount,
                    price=data.get('price'),
                    is_spot=data.get('is_spot', True)
                )
                
                # 实际订单执行成功
                result = {
                    'success': True,
                    'message': '订单提交成功',
                    'order': {
                        'order_id': order.get('id', order_id),
                        'exchange': data['exchange'],
                        'symbol': data['symbol'],
                        'side': data['side'],
                        'order_type': data['order_type'],
                        'amount': amount,
                        'price': data.get('price'),
                        'is_spot': data.get('is_spot', True),
                        'status': order.get('status', 'submitted'),
                        'created_at': datetime.now().isoformat(),
                        'exchange_order_id': order.get('id'),
                        'filled': order.get('filled', 0),
                        'remaining': order.get('remaining', amount),
                        'cost': order.get('cost', 0)
                    }
                }
                
            except Exception as order_error:
                # 如果实际订单执行失败，使用模拟模式
                print(f"实际订单执行失败，使用模拟模式: {str(order_error)}")
                
                # 模拟订单执行
                result = {
                    'success': True,
                    'message': '订单提交成功 (模拟模式)',
                    'order': {
                        'order_id': order_id,
                        'exchange': data['exchange'],
                        'symbol': data['symbol'],
                        'side': data['side'],
                        'order_type': data['order_type'],
                        'amount': amount,
                        'price': data.get('price'),
                        'is_spot': data.get('is_spot', True),
                        'status': 'submitted',
                        'created_at': datetime.now().isoformat(),
                        'exchange_order_id': f"mock_{order_id}",
                        'filled': 0,
                        'remaining': amount,
                        'cost': 0,
                        'note': '模拟模式 - 未实际执行交易'
                    }
                }

            return jsonify(result), 201

        except Exception as e:
            return jsonify({
                'success': False,
                'error': f'订单执行失败: {str(e)}'
            }), 500

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'处理请求失败: {str(e)}'
        }), 500


# ==================== 辅助接口 ====================

@hedging_blueprint.route('/config/symbols', methods=['GET'])
def get_trading_symbols():
    """
    获取支持的交易对列表
    """
    # 这里应该从交易所API获取实际的交易对列表
    # 暂时返回常用的交易对
    symbols = [
        'BTC/USDT', 'ETH/USDT', 'BNB/USDT', 'ADA/USDT', 'DOT/USDT',
        'SOL/USDT', 'MATIC/USDT', 'AVAX/USDT', 'LINK/USDT', 'UNI/USDT'
    ]

    return jsonify({
        'success': True,
        'symbols': symbols
    })
