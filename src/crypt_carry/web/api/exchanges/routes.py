"""
交易所API路由模块
定义与交易所相关的API端点
"""
from flask import jsonify
from . import exchanges_blueprint
from ..docs import exchanges_ns, exchange_info_model


@exchanges_blueprint.route('', methods=['GET'])
@exchanges_ns.doc('get_exchanges')
@exchanges_ns.marshal_list_with(exchange_info_model)
def get_exchanges():
    """
    获取所有支持的交易所

    返回:
        JSON格式的交易所列表，包含id, name等信息
    """
    # 实际项目中应该从配置或数据库中获取
    exchanges = [
        {
            'id': 'binance',
            'name': '币安',
            'spot_enabled': True,
            'futures_enabled': True
        },
        {
            'id': 'okx',
            'name': 'OKX',
            'spot_enabled': True,
            'futures_enabled': True
        }
    ]
    return jsonify(exchanges)


@exchanges_blueprint.route('/<exchange_id>/symbols', methods=['GET'])
def get_symbols(exchange_id):
    """
    获取指定交易所支持的交易对

    参数:
        exchange_id: 交易所ID

    返回:
        JSON格式的交易对列表
    """
    # 实际项目中应该从交易所客户端获取
    symbols = [
        {'symbol': 'BTC/USDT', 'base': 'BTC', 'quote': 'USDT'},
        {'symbol': 'ETH/USDT', 'base': 'ETH', 'quote': 'USDT'},
        {'symbol': 'SOL/USDT', 'base': 'SOL', 'quote': 'USDT'},
    ]

    # 可以根据交易所ID返回不同的交易对
    if exchange_id == 'okx':
        symbols.append({'symbol': 'ADA/USDT', 'base': 'ADA', 'quote': 'USDT'})
    elif exchange_id == 'binance':
        symbols.append({'symbol': 'BNB/USDT', 'base': 'BNB', 'quote': 'USDT'})

    return jsonify({
        'exchange_id': exchange_id,
        'symbols': symbols
    })


@exchanges_blueprint.route('/<exchange_id>/markets/<symbol>', methods=['GET'])
def get_market_info(exchange_id, symbol):
    """
    获取指定交易所指定交易对的市场信息

    参数:
        exchange_id: 交易所ID
        symbol: 交易对名称

    返回:
        JSON格式的市场信息
    """
    # 实际项目中应该从交易所客户端获取
    # 这里使用模拟数据
    market_info = {
        'exchange_id': exchange_id,
        'symbol': symbol,
        'price_precision': 2,
        'amount_precision': 4,
        'min_amount': 0.001,
        'min_cost': 10.0,
        'maker_fee': 0.001,
        'taker_fee': 0.001,
        'last_price': 50000.0 if 'BTC' in symbol else 3000.0 if 'ETH' in symbol else 100.0
    }

    return jsonify(market_info)


@exchanges_blueprint.route('/<exchange_id>/funding_rate/<symbol>', methods=['GET'])
def get_funding_rate(exchange_id, symbol):
    """
    获取指定交易所指定交易对的资金费率

    参数:
        exchange_id: 交易所ID
        symbol: 交易对名称

    返回:
        JSON格式的资金费率信息
    """
    # 实际项目中应该从交易所客户端获取
    # 这里使用模拟数据
    import random

    funding_rate = {
        'exchange_id': exchange_id,
        'symbol': symbol,
        'rate': round(random.uniform(-0.001, 0.003), 6),
        'next_funding_time': *************,  # 实际应该是未来的时间戳
        'estimated_rate': round(random.uniform(-0.001, 0.003), 6)
    }

    return jsonify(funding_rate)


@exchanges_blueprint.route('/<exchange_id>/funding_rates', methods=['GET'])
def get_all_funding_rates(exchange_id):
    """
    获取指定交易所所有交易对的资金费率

    参数:
        exchange_id: 交易所ID

    返回:
        JSON格式的资金费率列表
    """
    # 实际项目中应该从交易所客户端获取
    # 这里使用模拟数据
    import random

    symbols = ['BTC/USDT', 'ETH/USDT', 'SOL/USDT']

    funding_rates = [
        {
            'exchange_id': exchange_id,
            'symbol': symbol,
            'rate': round(random.uniform(-0.001, 0.003), 6),
            'next_funding_time': *************,  # 实际应该是未来的时间戳
            'estimated_rate': round(random.uniform(-0.001, 0.003), 6)
        }
        for symbol in symbols
    ]

    return jsonify({
        'exchange_id': exchange_id,
        'funding_rates': funding_rates
    })


@exchanges_blueprint.route('/<exchange_id>/balance/<account_type>', methods=['GET'])
def get_balance(exchange_id, account_type):
    """
    获取指定交易所账户余额

    参数:
        exchange_id: 交易所ID
        account_type: 账户类型(spot/futures)

    返回:
        JSON格式的账户余额
    """
    # 实际项目中应该从交易所客户端获取
    # 这里使用模拟数据

    if account_type == 'spot':
        balances = {
            'USDT': {'free': 1000.0, 'used': 0.0, 'total': 1000.0},
            'BTC': {'free': 0.01, 'used': 0.0, 'total': 0.01},
            'ETH': {'free': 0.5, 'used': 0.0, 'total': 0.5}
        }
    else:  # futures
        balances = {
            'USDT': {'free': 2000.0, 'used': 500.0, 'total': 2500.0},
            'unrealizedPnL': 50.0,
            'marginBalance': 2550.0,
            'maintenanceMargin': 100.0,
            'initialMargin': 500.0,
            'availableBalance': 2050.0
        }

    return jsonify({
        'exchange_id': exchange_id,
        'account_type': account_type,
        'balances': balances
    })
