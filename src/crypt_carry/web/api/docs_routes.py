"""
API文档路由实现
使用Flask-RESTX Resource类实现API文档
"""
from flask_restx import Resource
from .docs import (
    api, hedging_ns, exchanges_ns,
    hedging_order_request, hedging_order_response,
    single_order_request, single_order_response,
    api_response_model, strategy_info_model,
    exchange_info_model, funding_rate_model
)

# ==================== 套保相关API文档 ====================

@hedging_ns.route('/orders/hedging')
class HedgingOrderResource(Resource):
    @hedging_ns.doc('create_hedging_order', description='创建套保订单')
    @hedging_ns.expect(hedging_order_request, validate=True)
    @hedging_ns.marshal_with(hedging_order_response, code=201, description='套保订单创建成功')
    @hedging_ns.response(400, '请求参数错误', api_response_model)
    @hedging_ns.response(500, '服务器内部错误', api_response_model)
    def post(self):
        """
        创建套保订单
        
        根据指定的条件创建跨交易所套保订单，支持多种触发条件：
        - 价格差异 (price_difference)
        - 资金费率差异 (funding_rate_diff)  
        - 基差率 (basis_rate)
        
        系统会监控市场数据，当满足任一启用条件时自动执行套保交易。
        """
        # 实际实现在 hedging/routes.py 中
        pass

@hedging_ns.route('/orders/single')
class SingleOrderResource(Resource):
    @hedging_ns.doc('create_single_order', description='创建单独订单')
    @hedging_ns.expect(single_order_request, validate=True)
    @hedging_ns.marshal_with(single_order_response, code=201, description='单独订单创建成功')
    @hedging_ns.response(400, '请求参数错误', api_response_model)
    @hedging_ns.response(500, '服务器内部错误', api_response_model)
    def post(self):
        """
        创建单独订单
        
        在指定交易所创建单个买入或卖出订单，支持：
        - 市价单 (market)
        - 限价单 (limit)
        - 现货交易 (spot)
        - 合约交易 (futures)
        
        订单金额可以用USDT或USDC计价。
        """
        # 实际实现在 hedging/routes.py 中
        pass

@hedging_ns.route('/strategies')
class StrategiesResource(Resource):
    @hedging_ns.doc('get_strategies', description='获取所有套保策略')
    @hedging_ns.marshal_list_with(strategy_info_model, description='策略列表')
    @hedging_ns.response(200, '获取成功')
    def get(self):
        """
        获取所有套保策略
        
        返回系统中配置的所有套保策略信息，包括：
        - 策略基本信息
        - 激活状态
        - 创建和更新时间
        """
        # 实际实现在 hedging/routes.py 中
        pass

    @hedging_ns.doc('create_strategy', description='创建新的套保策略')
    @hedging_ns.expect(strategy_info_model, validate=True)
    @hedging_ns.marshal_with(strategy_info_model, code=201, description='策略创建成功')
    @hedging_ns.response(400, '请求参数错误', api_response_model)
    def post(self):
        """
        创建新的套保策略
        
        创建一个新的套保策略配置。
        """
        # 实际实现在 hedging/routes.py 中
        pass

# ==================== 交易所相关API文档 ====================

@exchanges_ns.route('')
class ExchangesResource(Resource):
    @exchanges_ns.doc('get_exchanges', description='获取所有支持的交易所')
    @exchanges_ns.marshal_list_with(exchange_info_model, description='交易所列表')
    @exchanges_ns.response(200, '获取成功')
    def get(self):
        """
        获取所有支持的交易所
        
        返回系统支持的所有交易所信息，包括：
        - 交易所ID和名称
        - 现货交易支持状态
        - 合约交易支持状态
        """
        # 实际实现在 exchanges/routes.py 中
        pass

@exchanges_ns.route('/<string:exchange_id>/funding_rate/<string:symbol>')
class FundingRateResource(Resource):
    @exchanges_ns.doc('get_funding_rate', description='获取指定交易所指定交易对的资金费率')
    @exchanges_ns.marshal_with(funding_rate_model, description='资金费率信息')
    @exchanges_ns.response(200, '获取成功')
    @exchanges_ns.response(404, '交易所或交易对不存在', api_response_model)
    def get(self, exchange_id, symbol):
        """
        获取资金费率
        
        获取指定交易所指定交易对的当前资金费率信息，包括：
        - 当前资金费率
        - 下次资金费率结算时间
        - 预估资金费率
        
        Args:
            exchange_id: 交易所ID (如: binance, okx, bybit)
            symbol: 交易对 (如: BTC/USDT, ETH/USDT)
        """
        # 实际实现在 exchanges/routes.py 中
        pass

@exchanges_ns.route('/<string:exchange_id>/symbols')
class SymbolsResource(Resource):
    @exchanges_ns.doc('get_symbols', description='获取指定交易所的所有交易对')
    @exchanges_ns.response(200, '获取成功')
    @exchanges_ns.response(404, '交易所不存在', api_response_model)
    def get(self, exchange_id):
        """
        获取交易对列表
        
        获取指定交易所支持的所有交易对信息。
        
        Args:
            exchange_id: 交易所ID (如: binance, okx, bybit)
        """
        # 实际实现在 exchanges/routes.py 中
        pass
