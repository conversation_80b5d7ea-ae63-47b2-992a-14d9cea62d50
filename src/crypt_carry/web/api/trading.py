"""
交易API蓝图
提供交易相关的接口，包括交易所信息、交易对数据等
"""
from flask import Blueprint, jsonify, request
import logging
import asyncio
import functools

# 创建蓝图
trading_blueprint = Blueprint('trading', __name__)

# 配置日志
logger = logging.getLogger(__name__)


# 异步转同步装饰器
def async_to_sync(f):
    @functools.wraps(f)
    def wrapper(*args, **kwargs):
        return asyncio.run(f(*args, **kwargs))

    return wrapper


@trading_blueprint.route('/exchanges', methods=['GET'])
def get_exchanges():
    """获取支持的交易所列表"""
    # 硬编码支持的交易所，后续可扩展
    exchanges = [
        {
            'id': 'binance',
            'name': '币安',
            'spot_enabled': True,
            'futures_enabled': True
        },
        {
            'id': 'okx',
            'name': 'OKX',
            'spot_enabled': True,
            'futures_enabled': True
        }
    ]
    return jsonify(exchanges)


@trading_blueprint.route('/symbols', methods=['GET'])
@async_to_sync
async def get_symbols():
    """获取交易对列表
    
    查询参数:
    - exchange: 交易所ID (必填)
    - market_type: 市场类型 (spot/futures)
    """
    exchange_id = request.args.get('exchange')
    market_type = request.args.get('market_type', 'futures')

    if not exchange_id:
        return jsonify({'error': '必须指定交易所参数'}), 400

    try:
        # 这里将使用您现有的系统获取交易对列表
        # 示例: 调用您系统中的方法获取交易对
        from crypt_carry.exchange.factory.exchange_factory import ExchangeFactory

        # 根据交易所ID获取交易所客户端
        factory = ExchangeFactory()
        exchange_client = await factory.create_exchange_client(exchange_id)

        # 获取交易对
        if market_type == 'spot':
            symbols = await exchange_client.get_spot_symbols()
        else:  # futures
            symbols = await exchange_client.get_futures_symbols()

        return jsonify(symbols)

    except ImportError:
        # 如果无法导入，返回测试数据
        logger.warning("无法导入ExchangeFactory，返回测试数据")
        test_symbols = generate_test_symbols(exchange_id, market_type)
        return jsonify(test_symbols)
    except Exception as e:
        logger.error(f"获取交易对失败: {str(e)}")
        return jsonify({'error': f'获取交易对失败: {str(e)}'}), 500


@trading_blueprint.route('/market-info', methods=['GET'])
@async_to_sync
async def get_market_info():
    """获取市场信息
    
    查询参数:
    - exchange: 交易所ID (必填)
    - symbol: 交易对 (必填)
    """
    exchange_id = request.args.get('exchange')
    symbol = request.args.get('symbol')

    if not exchange_id or not symbol:
        return jsonify({'error': '必须指定交易所和交易对参数'}), 400

    try:
        # 这里将使用您现有的系统获取市场信息
        # 示例: 调用您系统中的方法获取市场信息
        from crypt_carry.exchange.factory.exchange_factory import ExchangeFactory

        # 根据交易所ID获取交易所客户端
        factory = ExchangeFactory()
        exchange_client = await factory.create_exchange_client(exchange_id)

        # 获取市场信息
        market_info = await exchange_client.get_market_info(symbol)

        return jsonify(market_info)

    except ImportError:
        # 如果无法导入，返回测试数据
        logger.warning("无法导入ExchangeFactory，返回测试数据")
        test_market_info = generate_test_market_info(exchange_id, symbol)
        return jsonify(test_market_info)
    except Exception as e:
        logger.error(f"获取市场信息失败: {str(e)}")
        return jsonify({'error': f'获取市场信息失败: {str(e)}'}), 500


@trading_blueprint.route('/funding-rates', methods=['GET'])
@async_to_sync
async def get_funding_rates():
    """获取资金费率
    
    查询参数:
    - exchange: 交易所ID (必填)
    - symbol: 交易对 (可选，不提供则返回所有交易对)
    """
    exchange_id = request.args.get('exchange')
    symbol = request.args.get('symbol')

    if not exchange_id:
        return jsonify({'error': '必须指定交易所参数'}), 400

    try:
        # 这里将使用您现有的系统获取资金费率
        # 示例: 调用您系统中的方法获取资金费率
        from crypt_carry.exchange.funding.funding_rate_manager import FundingRateManager

        manager = FundingRateManager.get_instance()

        if symbol:
            # 获取特定交易对的资金费率
            funding_rate = await manager.get_funding_rate(exchange_id, symbol)
            result = {symbol: funding_rate}
        else:
            # 获取所有交易对的资金费率
            result = await manager.get_all_funding_rates(exchange_id)

        return jsonify(result)

    except ImportError:
        # 如果无法导入，返回测试数据
        logger.warning("无法导入FundingRateManager，返回测试数据")
        test_rates = generate_test_funding_rates(exchange_id, symbol)
        return jsonify(test_rates)
    except Exception as e:
        logger.error(f"获取资金费率失败: {str(e)}")
        return jsonify({'error': f'获取资金费率失败: {str(e)}'}), 500


@trading_blueprint.route('/balance', methods=['GET'])
@async_to_sync
async def get_balance():
    """获取账户余额
    
    查询参数:
    - exchange: 交易所ID (必填)
    - account_type: 账户类型 (spot/futures)
    """
    exchange_id = request.args.get('exchange')
    account_type = request.args.get('account_type', 'futures')

    if not exchange_id:
        return jsonify({'error': '必须指定交易所参数'}), 400

    try:
        # 这里将使用您现有的系统获取账户余额
        # 示例: 调用您系统中的方法获取账户余额
        from crypt_carry.exchange.account.balance_manager import BalanceManager

        manager = BalanceManager()

        if account_type == 'spot':
            balance = await manager.get_spot_balance(exchange_id)
        else:  # futures
            balance = await manager.get_futures_balance(exchange_id)

        return jsonify(balance)

    except ImportError:
        # 如果无法导入，返回测试数据
        logger.warning("无法导入BalanceManager，返回测试数据")
        test_balance = generate_test_balance(exchange_id, account_type)
        return jsonify(test_balance)
    except Exception as e:
        logger.error(f"获取账户余额失败: {str(e)}")
        return jsonify({'error': f'获取账户余额失败: {str(e)}'}), 500


# 辅助函数: 生成测试数据
def generate_test_symbols(exchange_id, market_type):
    """生成测试用的交易对数据"""
    if market_type == 'spot':
        return [
            {'symbol': 'BTC/USDT', 'base': 'BTC', 'quote': 'USDT'},
            {'symbol': 'ETH/USDT', 'base': 'ETH', 'quote': 'USDT'},
            {'symbol': 'SOL/USDT', 'base': 'SOL', 'quote': 'USDT'},
            {'symbol': 'BNB/USDT', 'base': 'BNB', 'quote': 'USDT'},
            {'symbol': 'XRP/USDT', 'base': 'XRP', 'quote': 'USDT'}
        ]
    else:  # futures
        return [
            {'symbol': 'BTC/USDT:USDT', 'base': 'BTC', 'quote': 'USDT', 'settle': 'USDT'},
            {'symbol': 'ETH/USDT:USDT', 'base': 'ETH', 'quote': 'USDT', 'settle': 'USDT'},
            {'symbol': 'SOL/USDT:USDT', 'base': 'SOL', 'quote': 'USDT', 'settle': 'USDT'},
            {'symbol': 'BNB/USDT:USDT', 'base': 'BNB', 'quote': 'USDT', 'settle': 'USDT'},
            {'symbol': 'XRP/USDT:USDT', 'base': 'XRP', 'quote': 'USDT', 'settle': 'USDT'}
        ]


def generate_test_market_info(exchange_id, symbol):
    """生成测试用的市场信息"""
    return {
        'symbol': symbol,
        'price_precision': 2,
        'amount_precision': 4,
        'min_amount': 0.001,
        'min_cost': 5.0,
        'maker_fee': 0.0002,
        'taker_fee': 0.0005,
        'last_price': 50000.0 if 'BTC' in symbol else 3000.0
    }


def generate_test_funding_rates(exchange_id, symbol=None):
    """生成测试用的资金费率数据"""
    if symbol:
        return {symbol: 0.0001}

    return {
        'BTC/USDT:USDT': 0.0001,
        'ETH/USDT:USDT': 0.0002,
        'SOL/USDT:USDT': 0.0003,
        'BNB/USDT:USDT': 0.0001,
        'XRP/USDT:USDT': 0.0002
    }


def generate_test_balance(exchange_id, account_type):
    """生成测试用的账户余额数据"""
    if account_type == 'spot':
        return {
            'USDT': {'free': 10000.0, 'used': 0.0, 'total': 10000.0},
            'BTC': {'free': 0.1, 'used': 0.0, 'total': 0.1},
            'ETH': {'free': 2.0, 'used': 0.0, 'total': 2.0}
        }
    else:  # futures
        return {
            'USDT': {'free': 10000.0, 'used': 1000.0, 'total': 11000.0},
            'unrealizedPnL': 500.0,
            'marginBalance': 11500.0,
            'maintenanceMargin': 500.0,
            'initialMargin': 1000.0,
            'availableBalance': 10500.0
        }
