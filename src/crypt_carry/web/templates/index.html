<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Crypt Carry - 加密货币套利系统</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 30px;
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        .header h1 {
            color: #2c3e50;
            margin-bottom: 10px;
        }
        .header p {
            color: #7f8c8d;
            font-size: 18px;
        }
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        .status-card {
            background: #f8f9fa;
            border-radius: 6px;
            padding: 20px;
            border-left: 4px solid #3498db;
        }
        .status-card h3 {
            margin: 0 0 10px 0;
            color: #2c3e50;
        }
        .status-card p {
            margin: 0;
            color: #7f8c8d;
        }
        .api-section {
            margin-top: 40px;
        }
        .api-section h2 {
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        .api-list {
            list-style: none;
            padding: 0;
        }
        .api-list li {
            background: #f8f9fa;
            margin: 10px 0;
            padding: 15px;
            border-radius: 4px;
            border-left: 3px solid #27ae60;
        }
        .api-list li code {
            background: #e9ecef;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: 'Monaco', 'Consolas', monospace;
        }
        .footer {
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ecf0f1;
            color: #95a5a6;
        }
    </style>
</head>
<body>
<div class="container">
    <div class="header">
        <h1>🚀 Crypt Carry</h1>
        <p>加密货币跨交易所套利系统</p>
    </div>

    <div class="status-grid">
        <div class="status-card">
            <h3>📊 系统状态</h3>
            <p>Web界面运行正常</p>
        </div>
        <div class="status-card">
            <h3>🔄 交易所连接</h3>
            <p>请查看API状态</p>
        </div>
        <div class="status-card">
            <h3>💰 套利策略</h3>
            <p>资金费率套利系统</p>
        </div>
        <div class="status-card">
            <h3>🛡️ 风险管理</h3>
            <p>自动持仓监控</p>
        </div>
    </div>

    <div class="api-section">
        <h2>📡 API 接口</h2>
        <ul class="api-list">
            <li>
                <strong>套保策略管理:</strong>
                <code>GET /api/hedging/strategies</code> - 获取策略列表
            </li>
            <li>
                <strong>交易所状态:</strong>
                <code>GET /api/exchanges/status</code> - 获取交易所连接状态
            </li>
            <li>
                <strong>持仓信息:</strong>
                <code>GET /api/hedging/positions</code> - 获取当前持仓
            </li>
            <li>
                <strong>策略配置:</strong>
                <code>POST /api/hedging/strategies</code> - 创建新策略
            </li>
        </ul>
    </div>

    <div class="footer">
        <p>© 2024 Crypt Carry - 专业的加密货币套利解决方案</p>
    </div>
</div>

<script>
    // 简单的状态检查
    document.addEventListener('DOMContentLoaded', function () {
        console.log('Crypt Carry Web Interface Loaded');

        // 可以在这里添加实时状态更新的JavaScript代码
        fetch('/api/exchanges/status')
            .then(response => response.json())
            .then(data => {
                console.log('Exchange Status:', data);
            })
            .catch(error => {
                console.log('API not available:', error);
            });
    });
</script>
</body>
</html>
