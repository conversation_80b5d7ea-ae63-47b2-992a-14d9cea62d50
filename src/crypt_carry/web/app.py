"""
简化版Flask应用主文件
提供轻量级的Web交互界面，不使用数据库和认证系统
"""
import os

from flask import Flask, jsonify, render_template
from flask_cors import CORS


def create_app():
    """
    创建简化版Flask应用实例
    """
    app = Flask(__name__,
                template_folder='templates',
                static_folder='static')

    # 基本配置
    app.config['SECRET_KEY'] = os.getenv('SECRET_KEY', 'dev_key_for_local_use_only')
    app.config['DEBUG'] = True

    # 启用CORS支持跨域请求，允许前端开发服务器访问
    CORS(app, resources={r"/api/*": {"origins": "*"}})

    # 注册API蓝图
    from crypt_carry.web.api.hedging import hedging_blueprint
    from crypt_carry.web.api.exchanges import exchanges_blueprint

    app.register_blueprint(hedging_blueprint)
    app.register_blueprint(exchanges_blueprint)

    # 注册错误处理
    @app.errorhandler(404)
    def not_found(error):
        return jsonify({'error': '请求的资源不存在'}), 404

    @app.errorhandler(500)
    def server_error(error):
        return jsonify({'error': '服务器内部错误'}), 500
    
    # API根路由
    @app.route('/api')
    def api_root():
        return jsonify({
            'status': 'online',
            'message': 'Crypt Carry API服务正在运行',
            'api_version': '1.0'
        })

    return app

def run_app():
    """
    运行Flask应用
    """
    app = create_app()
    app.run(host='0.0.0.0', port=5001, debug=True)

if __name__ == '__main__':
    run_app()
