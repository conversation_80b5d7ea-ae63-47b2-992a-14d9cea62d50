@echo off
REM Crypt Carry Web Services 启动脚本 (Windows版本)
REM 用于同时启动前后端服务

setlocal enabledelayedexpansion

REM 设置颜色代码
set "RED=[91m"
set "GREEN=[92m"
set "YELLOW=[93m"
set "BLUE=[94m"
set "NC=[0m"

REM 获取脚本所在目录
set "SCRIPT_DIR=%~dp0"
set "WEB_DIR=%SCRIPT_DIR%"
set "FRONTEND_DIR=%WEB_DIR%frontend"
for %%i in ("%SCRIPT_DIR%..\..\..") do set "PROJECT_ROOT=%%~fi"

echo %BLUE%[INFO]%NC% Crypt Carry Web Services 启动脚本
echo %BLUE%[INFO]%NC% 项目根目录: %PROJECT_ROOT%
echo %BLUE%[INFO]%NC% Web目录: %WEB_DIR%
echo %BLUE%[INFO]%NC% 前端目录: %FRONTEND_DIR%

REM 检查Python
python --version >nul 2>&1
if errorlevel 1 (
    echo %RED%[ERROR]%NC% Python 未安装，请先安装 Python
    pause
    exit /b 1
)

REM 检查Node.js
node --version >nul 2>&1
if errorlevel 1 (
    echo %RED%[ERROR]%NC% Node.js 未安装，请先安装 Node.js
    pause
    exit /b 1
)

REM 检查Yarn
yarn --version >nul 2>&1
if errorlevel 1 (
    echo %RED%[ERROR]%NC% Yarn 未安装，请先安装 Yarn
    pause
    exit /b 1
)

REM 检查端口占用
echo %BLUE%[INFO]%NC% 检查端口占用...
netstat -an | find "5000" | find "LISTENING" >nul
if not errorlevel 1 (
    echo %YELLOW%[WARNING]%NC% 端口 5000 已被占用
    set /p "kill_backend=是否要杀死占用进程? (y/N): "
    if /i "!kill_backend!"=="y" (
        for /f "tokens=5" %%a in ('netstat -ano ^| find "5000" ^| find "LISTENING"') do (
            taskkill /PID %%a /F >nul 2>&1
        )
        echo %GREEN%[SUCCESS]%NC% 已杀死占用进程
    ) else (
        echo %RED%[ERROR]%NC% 端口冲突，退出启动
        pause
        exit /b 1
    )
)

netstat -an | find "5173" | find "LISTENING" >nul
if not errorlevel 1 (
    echo %YELLOW%[WARNING]%NC% 端口 5173 已被占用
    set /p "kill_frontend=是否要杀死占用进程? (y/N): "
    if /i "!kill_frontend!"=="y" (
        for /f "tokens=5" %%a in ('netstat -ano ^| find "5173" ^| find "LISTENING"') do (
            taskkill /PID %%a /F >nul 2>&1
        )
        echo %GREEN%[SUCCESS]%NC% 已杀死占用进程
    ) else (
        echo %RED%[ERROR]%NC% 端口冲突，退出启动
        pause
        exit /b 1
    )
)

REM 检查虚拟环境
set "VENV_PATH=%PROJECT_ROOT%\venv"
if not exist "%VENV_PATH%" (
    echo %YELLOW%[WARNING]%NC% 虚拟环境不存在，正在创建...
    cd /d "%PROJECT_ROOT%"
    python -m venv venv
    echo %GREEN%[SUCCESS]%NC% 虚拟环境创建完成
)

REM 激活虚拟环境
echo %BLUE%[INFO]%NC% 激活虚拟环境...
call "%VENV_PATH%\Scripts\activate.bat"

REM 安装后端依赖
echo %BLUE%[INFO]%NC% 检查后端依赖...
cd /d "%WEB_DIR%"
if not exist "requirements.txt" (
    echo %RED%[ERROR]%NC% requirements.txt 不存在
    pause
    exit /b 1
)

pip list | find "Flask" >nul
if errorlevel 1 (
    echo %BLUE%[INFO]%NC% 安装后端依赖...
    pip install -r requirements.txt
    echo %GREEN%[SUCCESS]%NC% 后端依赖安装完成
)

REM 检查前端依赖
echo %BLUE%[INFO]%NC% 检查前端依赖...
cd /d "%FRONTEND_DIR%"
if not exist "package.json" (
    echo %RED%[ERROR]%NC% package.json 不存在
    pause
    exit /b 1
)

if not exist "node_modules" (
    echo %BLUE%[INFO]%NC% 安装前端依赖...
    yarn install
    echo %GREEN%[SUCCESS]%NC% 前端依赖安装完成
)

REM 创建日志目录
if not exist "%WEB_DIR%\logs" mkdir "%WEB_DIR%\logs"

REM 解析命令行参数
set "COMMAND=%1"
if "%COMMAND%"=="" set "COMMAND=start"

if "%COMMAND%"=="start" goto :start
if "%COMMAND%"=="stop" goto :stop
if "%COMMAND%"=="restart" goto :restart
if "%COMMAND%"=="status" goto :status
if "%COMMAND%"=="logs" goto :logs
if "%COMMAND%"=="help" goto :help
if "%COMMAND%"=="-h" goto :help
if "%COMMAND%"=="--help" goto :help

echo %RED%[ERROR]%NC% 未知命令: %COMMAND%
echo %BLUE%[INFO]%NC% 使用 '%~nx0 help' 查看帮助
pause
exit /b 1

:start
echo %BLUE%[INFO]%NC% 启动所有服务...

REM 启动后端服务
echo %BLUE%[INFO]%NC% 启动后端服务...
cd /d "%WEB_DIR%"
set "FLASK_APP=app.py"
set "FLASK_ENV=development"
set "FLASK_DEBUG=1"
set "PYTHONPATH=%PROJECT_ROOT%\src;%PYTHONPATH%"

start "Backend Service" /min cmd /c "python app.py > logs\backend.log 2>&1"

REM 等待后端启动
timeout /t 5 /nobreak >nul

REM 检查后端服务
curl -s http://localhost:5000/health >nul 2>&1
if errorlevel 1 (
    echo %RED%[ERROR]%NC% 后端服务启动失败
    pause
    exit /b 1
) else (
    echo %GREEN%[SUCCESS]%NC% 后端服务启动成功
)

REM 启动前端服务
echo %BLUE%[INFO]%NC% 启动前端服务...
cd /d "%FRONTEND_DIR%"
start "Frontend Service" /min cmd /c "yarn dev > ..\logs\frontend.log 2>&1"

REM 等待前端启动
timeout /t 8 /nobreak >nul

REM 检查前端服务
curl -s http://localhost:5173 >nul 2>&1
if errorlevel 1 (
    echo %RED%[ERROR]%NC% 前端服务启动失败
    pause
    exit /b 1
) else (
    echo %GREEN%[SUCCESS]%NC% 前端服务启动成功
)

echo.
echo %GREEN%[SUCCESS]%NC% 所有服务启动完成!
echo %BLUE%[INFO]%NC% 后端服务: http://localhost:5000
echo %BLUE%[INFO]%NC% 前端服务: http://localhost:5173
echo %BLUE%[INFO]%NC% API文档: http://localhost:5000/api/docs
echo.
echo %BLUE%[INFO]%NC% 按任意键停止所有服务...
pause >nul

REM 停止服务
taskkill /F /FI "WINDOWTITLE eq Backend Service*" >nul 2>&1
taskkill /F /FI "WINDOWTITLE eq Frontend Service*" >nul 2>&1
echo %GREEN%[SUCCESS]%NC% 所有服务已停止
goto :end

:stop
echo %BLUE%[INFO]%NC% 正在停止服务...
taskkill /F /FI "WINDOWTITLE eq Backend Service*" >nul 2>&1
taskkill /F /FI "WINDOWTITLE eq Frontend Service*" >nul 2>&1
taskkill /F /IM python.exe /FI "COMMANDLINE eq *app.py*" >nul 2>&1
taskkill /F /IM node.exe /FI "COMMANDLINE eq *vite*" >nul 2>&1
echo %GREEN%[SUCCESS]%NC% 所有服务已停止
goto :end

:restart
call :stop
timeout /t 2 /nobreak >nul
call :start
goto :end

:status
echo %BLUE%[INFO]%NC% 检查服务状态...

curl -s http://localhost:5000/health >nul 2>&1
if errorlevel 1 (
    echo %RED%[ERROR]%NC% 后端服务未运行
) else (
    echo %GREEN%[SUCCESS]%NC% 后端服务运行中 (http://localhost:5000)
)

curl -s http://localhost:5173 >nul 2>&1
if errorlevel 1 (
    echo %RED%[ERROR]%NC% 前端服务未运行
) else (
    echo %GREEN%[SUCCESS]%NC% 前端服务运行中 (http://localhost:5173)
)
goto :end

:logs
echo %BLUE%[INFO]%NC% 查看服务日志...
echo === 后端日志 ===
if exist "%WEB_DIR%\logs\backend.log" (
    powershell "Get-Content '%WEB_DIR%\logs\backend.log' -Tail 20"
) else (
    echo 无后端日志
)
echo.
echo === 前端日志 ===
if exist "%WEB_DIR%\logs\frontend.log" (
    powershell "Get-Content '%WEB_DIR%\logs\frontend.log' -Tail 20"
) else (
    echo 无前端日志
)
goto :end

:help
echo 用法: %~nx0 [命令]
echo.
echo 命令:
echo   start    启动所有服务 (默认)
echo   stop     停止所有服务
echo   restart  重启所有服务
echo   status   检查服务状态
echo   logs     查看服务日志
echo   help     显示此帮助信息
echo.
echo 示例:
echo   %~nx0 start    # 启动服务
echo   %~nx0 status   # 检查状态
echo   %~nx0 logs     # 查看日志
goto :end

:end
if not "%COMMAND%"=="start" pause
exit /b 0
