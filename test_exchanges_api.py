#!/usr/bin/env python3
"""
测试交易所API的简单脚本
"""
import requests
import json

# API基础URL
BASE_URL = "http://localhost:5001/api"

def test_exchanges_list():
    """测试获取交易所列表"""
    print("🔍 测试获取交易所列表...")
    try:
        response = requests.get(f"{BASE_URL}/exchanges")
        print(f"状态码: {response.status_code}")
        data = response.json()
        print(f"响应: {json.dumps(data, indent=2, ensure_ascii=False)}")
        return response.status_code == 200
    except Exception as e:
        print(f"❌ 错误: {e}")
        return False

def test_exchange_symbols():
    """测试获取交易所交易对"""
    print("\n🔍 测试获取交易所交易对...")
    try:
        response = requests.get(f"{BASE_URL}/exchanges/binance/symbols")
        print(f"状态码: {response.status_code}")
        data = response.json()
        print(f"响应: {json.dumps(data, indent=2, ensure_ascii=False)}")
        return response.status_code == 200
    except Exception as e:
        print(f"❌ 错误: {e}")
        return False

def test_market_info():
    """测试获取市场信息"""
    print("\n🔍 测试获取市场信息...")
    try:
        # 使用不带斜杠的交易对符号
        symbol = "BTCUSDT"
        response = requests.get(f"{BASE_URL}/exchanges/binance/markets/{symbol}")
        print(f"状态码: {response.status_code}")
        data = response.json()
        print(f"响应: {json.dumps(data, indent=2, ensure_ascii=False)}")
        return response.status_code == 200
    except Exception as e:
        print(f"❌ 错误: {e}")
        return False

def test_funding_rate():
    """测试获取资金费率"""
    print("\n🔍 测试获取资金费率...")
    try:
        # 使用不带斜杠的交易对符号
        symbol = "BTCUSDT"
        response = requests.get(f"{BASE_URL}/exchanges/binance/funding_rate/{symbol}")
        print(f"状态码: {response.status_code}")
        data = response.json()
        print(f"响应: {json.dumps(data, indent=2, ensure_ascii=False)}")
        return response.status_code == 200
    except Exception as e:
        print(f"❌ 错误: {e}")
        return False

def test_all_funding_rates():
    """测试获取所有资金费率"""
    print("\n🔍 测试获取所有资金费率...")
    try:
        response = requests.get(f"{BASE_URL}/exchanges/okx/funding_rates")
        print(f"状态码: {response.status_code}")
        data = response.json()
        print(f"响应: {json.dumps(data, indent=2, ensure_ascii=False)}")
        return response.status_code == 200
    except Exception as e:
        print(f"❌ 错误: {e}")
        return False

def test_balance():
    """测试获取账户余额"""
    print("\n🔍 测试获取账户余额...")
    try:
        response = requests.get(f"{BASE_URL}/exchanges/binance/balance/spot")
        print(f"状态码: {response.status_code}")
        data = response.json()
        print(f"响应: {json.dumps(data, indent=2, ensure_ascii=False)}")
        return response.status_code == 200
    except Exception as e:
        print(f"❌ 错误: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试交易所API...")

    tests = [
        ("获取交易所列表", test_exchanges_list),
        ("获取交易对", test_exchange_symbols),
        ("获取市场信息", test_market_info),
        ("获取资金费率", test_funding_rate),
        ("获取所有资金费率", test_all_funding_rates),
        ("获取账户余额", test_balance),
    ]

    results = []
    for test_name, test_func in tests:
        success = test_func()
        results.append((test_name, success))
        if success:
            print(f"✅ {test_name} 测试通过")
        else:
            print(f"❌ {test_name} 测试失败")

    print("\n📊 测试结果汇总:")
    passed = sum(1 for _, success in results if success)
    total = len(results)
    print(f"通过: {passed}/{total}")

    for test_name, success in results:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"  {test_name}: {status}")

    if passed == total:
        print("\n🎉 所有交易所API测试通过！")
    else:
        print("\n⚠️  部分测试失败，请检查API实现")

if __name__ == "__main__":
    main()
