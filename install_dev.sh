#!/bin/bash
# 一键安装开发模式脚本

echo "===== 开始安装 crypt_carry 开发环境 ====="

# 检查Python版本
python_version=$(python3 --version 2>&1 | awk '{print $2}')
echo "检测到Python版本: $python_version"
if [[ ! "$python_version" =~ ^3\.1[0-9] ]]; then
    echo "警告: 推荐使用Python 3.10以上版本，当前版本为 $python_version"
    read -p "是否继续安装? (y/n): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "安装已取消"
        exit 1
    fi
fi

# 创建虚拟环境
echo "正在创建虚拟环境..."
python3 -m venv venv
source venv/bin/activate

# 升级pip
echo "正在升级pip..."
pip install --upgrade pip

# 安装开发模式
echo "正在安装开发模式依赖..."
pip install -e ".[dev]"

# 安装web模块依赖
echo "正在安装web模块依赖..."
pip install flask flask-restful flask-cors flask-jwt-extended flask-sqlalchemy

# 验证安装
echo "验证安装..."
pip list | grep crypt_carry

echo "===== crypt_carry 开发环境安装完成 ====="
echo "激活虚拟环境: source venv/bin/activate"
echo "启动应用: python -m scripts.run_carry"
echo "启动Web服务: python -m crypt_carry.web.app"
