import unittest
from pathlib import Path

# 计算项目根目录
project_root = Path(__file__).parent.parent

from carry.config.config_loader import ConfigLoader
from carry.analysis.basis_calculator import BasisCalculator
from carry.utils.arbitrage_manager import ArbitrageManager


class TestBasisCalculator(unittest.TestCase):
    def setUp(self):
        self.config = ConfigLoader.get_base_config()
        self.trading_config = ConfigLoader.get_trading_config()
        self.arbitrage_manager = ArbitrageManager()
        self.calculator = BasisCalculator(self.arbitrage_manager)

    def test_basis_calculation(self):
        """测试基差计算逻辑"""
        # 测试数据
        test_exchanges_data = {
            "timestamp": 1733025657028,
            "data": {
                "exchanges": {
                    "Binance": {
                        "latest": {
                            "BTC": {
                                "USDT": {
                                    "SPOT": 50000,
                                    "FUTURE": 50500
                                },
                                "timestamp": 1733025657027
                            }
                        }
                    },
                    "OKX": {
                        "latest": {
                            "BTC": {
                                "USDT": {
                                    "SPOT": 49800,
                                    "FUTURE": 50200
                                },
                                "timestamp": 1733025657027
                            }
                        }
                    }
                }
            }
        }

        # 设置基差率阈值
        self.trading_config["THRESHOLDS"]["BASIS_RATE"] = 0.5

        # 执行基差计算
        self.calculator.calculate_basis(
            test_exchanges_data['data']['exchanges'],
            'BTC',
            'USDT'
        )

        print("基差计算测试完成")

    def test_cross_exchange_basis(self):
        """测试跨交易所基差计算"""
        test_exchanges_data = {
            "timestamp": 1733025657028,
            "data": {
                "exchanges": {
                    "Binance": {
                        "latest": {
                            "BTC": {
                                "USDT": {
                                    "SPOT": 50000,
                                    "FUTURE": 60000
                                },
                                "timestamp": 1733025657027
                            }
                        }
                    },
                    "OKX": {
                        "latest": {
                            "BTC": {
                                "USDT": {
                                    "SPOT": 60000,
                                    "FUTURE": 51000
                                },
                                "timestamp": 1733025657027
                            }
                        }
                    }
                }
            }
        }

        self.trading_config["THRESHOLDS"]["BASIS_RATE"] = 0.5

        self.calculator.calculate_basis(
            test_exchanges_data['data']['exchanges'],
            'BTC',
            'USDT'
        )

        print("跨交易所基差计算测试完成")


def main():
    print("开始执行基差计算测试...")

    try:
        unittest.main()
        print("所有测试通过！")
    except Exception as e:
        print(f"测试失败：{e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
